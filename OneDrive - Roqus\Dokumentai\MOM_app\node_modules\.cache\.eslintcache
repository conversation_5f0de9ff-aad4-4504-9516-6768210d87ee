[{"C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\index.ts": "3", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\index.ts": "4", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useTranscription.ts": "5", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useAudioRecorder.ts": "6", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingButton.tsx": "7", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingIndicator.tsx": "8", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptViewer.tsx": "9", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\MeetingsList.tsx": "10", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ErrorBoundary.tsx": "11", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\AudioPlayer.tsx": "12", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\utils\\demoData.ts": "13", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\whisperService.ts": "14", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\config\\whisper.ts": "15", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperConfig.tsx": "16", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptionManager.tsx": "17", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ProfessionalTranscriptViewer.tsx": "18", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\speakerService.ts": "19", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingPanel.tsx": "20", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\CollapsibleTranscriptsList.tsx": "21", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperStatusIndicator.tsx": "22", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\GridControls.tsx": "23"}, {"size": 274, "mtime": 1752991530351, "results": "24", "hashOfConfig": "25"}, {"size": 24887, "mtime": 1753033023155, "results": "26", "hashOfConfig": "25"}, {"size": 111, "mtime": 1752991530350, "results": "27", "hashOfConfig": "25"}, {"size": 762, "mtime": 1752992666618, "results": "28", "hashOfConfig": "25"}, {"size": 10587, "mtime": 1752991532046, "results": "29", "hashOfConfig": "25"}, {"size": 5709, "mtime": 1752909986107, "results": "30", "hashOfConfig": "25"}, {"size": 2713, "mtime": 1752991532046, "results": "31", "hashOfConfig": "25"}, {"size": 3321, "mtime": 1752991532174, "results": "32", "hashOfConfig": "25"}, {"size": 7791, "mtime": 1752991532046, "results": "33", "hashOfConfig": "25"}, {"size": 6669, "mtime": 1752996627640, "results": "34", "hashOfConfig": "25"}, {"size": 4087, "mtime": 1752991532175, "results": "35", "hashOfConfig": "25"}, {"size": 8068, "mtime": 1752991532177, "results": "36", "hashOfConfig": "25"}, {"size": 4195, "mtime": 1752991534666, "results": "37", "hashOfConfig": "25"}, {"size": 10086, "mtime": 1752991532048, "results": "38", "hashOfConfig": "25"}, {"size": 1243, "mtime": 1752991532046, "results": "39", "hashOfConfig": "25"}, {"size": 4783, "mtime": 1752991532048, "results": "40", "hashOfConfig": "25"}, {"size": 10492, "mtime": 1752996047521, "results": "41", "hashOfConfig": "25"}, {"size": 6657, "mtime": 1752996635153, "results": "42", "hashOfConfig": "25"}, {"size": 9878, "mtime": 1752991532046, "results": "43", "hashOfConfig": "25"}, {"size": 4124, "mtime": 1752995765143, "results": "44", "hashOfConfig": "25"}, {"size": 19278, "mtime": 1752996569917, "results": "45", "hashOfConfig": "25"}, {"size": 966, "mtime": 1752993800647, "results": "46", "hashOfConfig": "25"}, {"size": 6661, "mtime": 1752996515996, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "scgkdl", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useTranscription.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useAudioRecorder.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\MeetingsList.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\AudioPlayer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\utils\\demoData.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\whisperService.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\config\\whisper.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperConfig.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptionManager.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ProfessionalTranscriptViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\speakerService.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingPanel.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\CollapsibleTranscriptsList.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperStatusIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\GridControls.tsx", [], []]