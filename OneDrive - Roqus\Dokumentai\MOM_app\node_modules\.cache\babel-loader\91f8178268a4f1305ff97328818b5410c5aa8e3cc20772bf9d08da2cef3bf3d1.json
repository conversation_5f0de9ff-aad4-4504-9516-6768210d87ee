{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\WhisperStatusIndicator.tsx\";\nimport React from 'react';\nimport { Zap, Check } from 'lucide-react';\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const WhisperStatusIndicator = () => {\n  const isConfigured = isWhisperConfigured();\n  const status = getConfigStatus();\n  const isConnected = isConfigured; // Assuming isConnected is derived from isConfigured\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"inline-flex items-center space-x-1.5 px-2 py-1 bg-gradient-to-r from-green-500/20 via-emerald-500/15 to-green-500/20 backdrop-blur-md border border-green-400/30 rounded-md shadow-sm\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} shadow-sm`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Zap, {\n      className: \"h-3 w-3 text-green-300\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-xs font-medium text-white/90\",\n      children: \"Whisper API\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), isConnected && /*#__PURE__*/_jsxDEV(Check, {\n      className: \"h-3 w-3 text-green-300\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 23\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = WhisperStatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"WhisperStatusIndicator\");", "map": {"version": 3, "names": ["React", "Zap", "Check", "isWhisperConfigured", "getConfigStatus", "jsxDEV", "_jsxDEV", "WhisperStatusIndicator", "isConfigured", "status", "isConnected", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperStatusIndicator.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Check<PERSON>ircle2, <PERSON>Circle, <PERSON><PERSON><PERSON><PERSON>gle, Zap, Check } from 'lucide-react';\r\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\r\n\r\nexport const WhisperStatusIndicator: React.FC = () => {\r\n  const isConfigured = isWhisperConfigured();\r\n  const status = getConfigStatus();\r\n  const isConnected = isConfigured; // Assuming isConnected is derived from isConfigured\r\n\r\n  return (\r\n    <div className=\"inline-flex items-center space-x-1.5 px-2 py-1 bg-gradient-to-r from-green-500/20 via-emerald-500/15 to-green-500/20 backdrop-blur-md border border-green-400/30 rounded-md shadow-sm\">\r\n      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} shadow-sm`}></div>\r\n      <Zap className=\"h-3 w-3 text-green-300\" />\r\n      <span className=\"text-xs font-medium text-white/90\">Whisper API</span>\r\n      {isConnected && <Check className=\"h-3 w-3 text-green-300\" />}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAA+CC,GAAG,EAAEC,KAAK,QAAQ,cAAc;AAC/E,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,OAAO,MAAMC,sBAAgC,GAAGA,CAAA,KAAM;EACpD,MAAMC,YAAY,GAAGL,mBAAmB,CAAC,CAAC;EAC1C,MAAMM,MAAM,GAAGL,eAAe,CAAC,CAAC;EAChC,MAAMM,WAAW,GAAGF,YAAY,CAAC,CAAC;;EAElC,oBACEF,OAAA;IAAKK,SAAS,EAAC,uLAAuL;IAAAC,QAAA,gBACpMN,OAAA;MAAKK,SAAS,EAAE,wBAAwBD,WAAW,GAAG,cAAc,GAAG,YAAY;IAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvGV,OAAA,CAACL,GAAG;MAACU,SAAS,EAAC;IAAwB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1CV,OAAA;MAAMK,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrEN,WAAW,iBAAIJ,OAAA,CAACJ,KAAK;MAACS,SAAS,EAAC;IAAwB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzD,CAAC;AAEV,CAAC;AAACC,EAAA,GAbWV,sBAAgC;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}