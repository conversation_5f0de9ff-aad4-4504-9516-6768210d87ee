{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\TranscriptionManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Clock, FileText, Zap, CheckCircle2, Loader2, XCircle, Trash2, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TranscriptionManager = ({\n  meetings,\n  onStartTranscription,\n  onCancelTranscription,\n  isTranscribing,\n  currentTranscriptionId,\n  onDeleteMeeting\n}) => {\n  _s();\n  const [selectedMeeting, setSelectedMeeting] = useState(null);\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const hrs = Math.floor(mins / 60);\n    const remainingMins = mins % 60;\n    if (hrs > 0) {\n      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\n    }\n    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;\n  };\n  const formatDate = date => {\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      return date.toLocaleTimeString('lt-LT', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffInHours < 168) {\n      // 7 dienos\n      return date.toLocaleDateString('lt-LT', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('lt-LT', {\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status.state) {\n      case 'not_started':\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 16\n        }, this);\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-4 w-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 16\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Loader2, {\n          className: \"h-4 w-4 text-blue-500 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(CheckCircle2, {\n          className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-4 w-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 16\n        }, this);\n      case 'cancelled':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-4 w-4 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusText = status => {\n    switch (status.state) {\n      case 'not_started':\n        return 'Nėra transkribavimo';\n      case 'pending':\n        return 'Eilėje';\n      case 'processing':\n        return `Apdorojama ${status.progress ? `(${status.progress}%)` : ''}`;\n      case 'completed':\n        return 'Baigtas';\n      case 'failed':\n        return `Klaida: ${status.error || 'Nežinoma klaida'}`;\n      case 'cancelled':\n        return 'Atšauktas';\n      default:\n        return 'Nežinomas statusas';\n    }\n  };\n  const canStartTranscription = meeting => {\n    return meeting.audioBlob !== undefined && meeting.transcriptionStatus.state === 'not_started' && !isTranscribing;\n  };\n  const canCancelTranscription = meeting => {\n    return (meeting.transcriptionStatus.state === 'processing' || meeting.transcriptionStatus.state === 'pending') && currentTranscriptionId === meeting.id;\n  };\n  const estimateTranscriptionCost = duration => {\n    const costPerMinute = 0.0055; // €0.0055 per minute\n    const minutes = duration / 60;\n    const estimatedCost = minutes * costPerMinute;\n    if (estimatedCost < 0.01) {\n      return '<€0.01';\n    }\n    return `~€${estimatedCost.toFixed(2)}`;\n  };\n\n  // Filtruoti tik meetings su audio\n  const availableMeetings = meetings.filter(m => m.audioBlob);\n  if (availableMeetings.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 transition-smooth hover:shadow-primary\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-br from-amber-100/80 via-orange-100/60 to-yellow-100/80 rounded-full flex items-center justify-center mx-auto mb-3 border border-gradient-to-r from-amber-200/40 to-orange-200/40 shadow-soft\",\n          children: /*#__PURE__*/_jsxDEV(FileText, {\n            className: \"h-6 w-6 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-1\",\n          children: \"N\\u0117ra \\u012Fra\\u0161\\u0173 transkribavimui\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"Sukurkite audio \\u012Fra\\u0161\\u0105, kad gal\\u0117tum\\u0117te j\\u012F transkribuoti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 19\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  const pendingMeetings = meetings.filter(m => m.transcriptionStatus.state === 'not_started' || m.transcriptionStatus.state === 'pending' || m.transcriptionStatus.state === 'processing');\n  const completedMeetings = meetings.filter(m => m.transcriptionStatus.state === 'completed');\n  const failedMeetings = meetings.filter(m => m.transcriptionStatus.state === 'failed');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col space-y-6\",\n    children: [pendingMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-white flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-5 w-5 text-yellow-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Laukia transkribavimo (\", pendingMeetings.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: pendingMeetings.map(meeting => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-white\",\n                children: meeting.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-white/70\",\n                children: [meeting.date.toLocaleString('lt-LT'), \" \\u2022 \", meeting.duration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), meeting.transcriptionStatus.state === 'processing' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-blue-400\",\n                    children: [\"Transkribuojama... \", meeting.transcriptionStatus.progress || 0, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onDeleteMeeting(meeting.id),\n              className: \"p-2 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200\",\n              title: \"I\\u0161trinti pokalb\\u012F\",\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this)\n        }, meeting.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this), completedMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-white flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-5 w-5 text-green-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"S\\u0117kmingai transkribuota (\", completedMeetings.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: completedMeetings.map(meeting => {\n          var _meeting$participants;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-white\",\n                  children: meeting.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-white/70\",\n                  children: [meeting.date.toLocaleString('lt-LT'), \" \\u2022 \", meeting.duration, \"s\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), meeting.transcript && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-green-400 mt-1\",\n                  children: [meeting.transcript.length, \" segment\\u0173 \\u2022 \", ((_meeting$participants = meeting.participants) === null || _meeting$participants === void 0 ? void 0 : _meeting$participants.length) || 0, \" dalyvi\\u0173\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onDeleteMeeting(meeting.id),\n                className: \"p-2 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200\",\n                title: \"I\\u0161trinti pokalb\\u012F\",\n                children: /*#__PURE__*/_jsxDEV(Trash2, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, meeting.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 9\n    }, this), failedMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-white flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-5 w-5 text-red-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Nepavyko transkribuoti (\", failedMeetings.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: failedMeetings.map(meeting => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-white\",\n                children: meeting.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-white/70\",\n                children: [meeting.date.toLocaleString('lt-LT'), \" \\u2022 \", meeting.duration, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this), meeting.transcriptionStatus.error && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-red-400 mt-1\",\n                children: [\"Klaida: \", meeting.transcriptionStatus.error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onDeleteMeeting(meeting.id),\n              className: \"p-2 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200\",\n              title: \"I\\u0161trinti pokalb\\u012F\",\n              children: /*#__PURE__*/_jsxDEV(Trash2, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this)\n        }, meeting.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this), meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col items-center justify-center text-center space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-16 h-16 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(Zap, {\n          className: \"h-8 w-8 text-purple-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-white\",\n          children: \"N\\u0117ra pokalbi\\u0173\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-white/70\",\n          children: \"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n};\n_s(TranscriptionManager, \"XxAPKUsFCrx+hRK8SN4fI21ZHHg=\");\n_c = TranscriptionManager;\nvar _c;\n$RefreshReg$(_c, \"TranscriptionManager\");", "map": {"version": 3, "names": ["React", "useState", "Clock", "FileText", "Zap", "CheckCircle2", "Loader2", "XCircle", "Trash2", "CheckCircle", "jsxDEV", "_jsxDEV", "TranscriptionManager", "meetings", "onStartTranscription", "onCancelTranscription", "isTranscribing", "currentTranscriptionId", "onDeleteMeeting", "_s", "selectedMeeting", "setSelectedMeeting", "formatDuration", "seconds", "mins", "Math", "floor", "hrs", "remainingMins", "toString", "padStart", "formatDate", "date", "now", "Date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "month", "day", "getStatusIcon", "status", "state", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusText", "progress", "error", "canStartTranscription", "meeting", "audioBlob", "undefined", "transcriptionStatus", "canCancelTranscription", "id", "estimateTranscriptionCost", "duration", "costPerMinute", "minutes", "estimatedCost", "toFixed", "availableMeetings", "filter", "m", "length", "children", "pendingMeetings", "completedMeetings", "failedMeetings", "map", "title", "toLocaleString", "onClick", "_meeting$participants", "transcript", "participants", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/TranscriptionManager.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Play, Square, Clock, Users, FileText, Zap, AlertTriangle, CheckCircle2, Loader2, XCircle, Trash2, CheckCircle } from 'lucide-react';\r\nimport { Meeting, TranscriptionStatus } from '../types/meeting';\r\n\r\ninterface TranscriptionManagerProps {\r\n  meetings: Meeting[];\r\n  onStartTranscription: (meetingId: string) => void;\r\n  onCancelTranscription: (meetingId: string) => void;\r\n  isTranscribing: boolean;\r\n  currentTranscriptionId?: string | null;\r\n  onDeleteMeeting: (meetingId: string) => void;\r\n}\r\n\r\nexport const TranscriptionManager: React.FC<TranscriptionManagerProps> = ({\r\n  meetings,\r\n  onStartTranscription,\r\n  onCancelTranscription,\r\n  isTranscribing,\r\n  currentTranscriptionId,\r\n  onDeleteMeeting,\r\n}) => {\r\n  const [selectedMeeting, setSelectedMeeting] = useState<string | null>(null);\r\n\r\n  const formatDuration = (seconds: number): string => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const hrs = Math.floor(mins / 60);\r\n    const remainingMins = mins % 60;\r\n    \r\n    if (hrs > 0) {\r\n      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n    }\r\n    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const formatDate = (date: Date): string => {\r\n    const now = new Date();\r\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\r\n    \r\n    if (diffInHours < 24) {\r\n      return date.toLocaleTimeString('lt-LT', { \r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    } else if (diffInHours < 168) { // 7 dienos\r\n      return date.toLocaleDateString('lt-LT', { \r\n        weekday: 'short',\r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    } else {\r\n      return date.toLocaleDateString('lt-LT', { \r\n        month: 'short', \r\n        day: 'numeric',\r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status: TranscriptionStatus) => {\r\n    switch (status.state) {\r\n      case 'not_started':\r\n        return <FileText className=\"h-4 w-4 text-gray-400\" />;\r\n      case 'pending':\r\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />;\r\n      case 'processing':\r\n        return <Loader2 className=\"h-4 w-4 text-blue-500 animate-spin\" />;\r\n      case 'completed':\r\n        return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n      case 'failed':\r\n        return <XCircle className=\"h-4 w-4 text-red-500\" />;\r\n      case 'cancelled':\r\n        return <XCircle className=\"h-4 w-4 text-gray-500\" />;\r\n      default:\r\n        return <FileText className=\"h-4 w-4 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusText = (status: TranscriptionStatus) => {\r\n    switch (status.state) {\r\n      case 'not_started':\r\n        return 'Nėra transkribavimo';\r\n      case 'pending':\r\n        return 'Eilėje';\r\n      case 'processing':\r\n        return `Apdorojama ${status.progress ? `(${status.progress}%)` : ''}`;\r\n      case 'completed':\r\n        return 'Baigtas';\r\n      case 'failed':\r\n        return `Klaida: ${status.error || 'Nežinoma klaida'}`;\r\n      case 'cancelled':\r\n        return 'Atšauktas';\r\n      default:\r\n        return 'Nežinomas statusas';\r\n    }\r\n  };\r\n\r\n  const canStartTranscription = (meeting: Meeting): boolean => {\r\n    return (\r\n      meeting.audioBlob !== undefined &&\r\n      meeting.transcriptionStatus.state === 'not_started' &&\r\n      !isTranscribing\r\n    );\r\n  };\r\n\r\n  const canCancelTranscription = (meeting: Meeting): boolean => {\r\n    return (\r\n      meeting.transcriptionStatus.state === 'processing' ||\r\n      meeting.transcriptionStatus.state === 'pending'\r\n    ) && currentTranscriptionId === meeting.id;\r\n  };\r\n\r\n  const estimateTranscriptionCost = (duration: number): string => {\r\n    const costPerMinute = 0.0055; // €0.0055 per minute\r\n    const minutes = duration / 60;\r\n    const estimatedCost = minutes * costPerMinute;\r\n    \r\n    if (estimatedCost < 0.01) {\r\n      return '<€0.01';\r\n    }\r\n    return `~€${estimatedCost.toFixed(2)}`;\r\n  };\r\n\r\n  // Filtruoti tik meetings su audio\r\n  const availableMeetings = meetings.filter(m => m.audioBlob);\r\n\r\n  if (availableMeetings.length === 0) {\r\n    return (\r\n      <div className=\"gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 transition-smooth hover:shadow-primary\">\r\n                  <div className=\"text-center\">\r\n            <div className=\"w-12 h-12 bg-gradient-to-br from-amber-100/80 via-orange-100/60 to-yellow-100/80 rounded-full flex items-center justify-center mx-auto mb-3 border border-gradient-to-r from-amber-200/40 to-orange-200/40 shadow-soft\">\r\n            <FileText className=\"h-6 w-6 text-gray-400\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-medium text-gray-900 mb-1\">Nėra įrašų transkribavimui</h3>\r\n          <p className=\"text-sm text-gray-500\">\r\n            Sukurkite audio įrašą, kad galėtumėte jį transkribuoti\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const pendingMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'not_started' || \r\n    m.transcriptionStatus.state === 'pending' ||\r\n    m.transcriptionStatus.state === 'processing'\r\n  );\r\n\r\n  const completedMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'completed'\r\n  );\r\n\r\n  const failedMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'failed'\r\n  );\r\n\r\n  return (\r\n    <div className=\"flex-1 flex flex-col space-y-6\">\r\n      {/* Pending Transcriptions */}\r\n      {pendingMeetings.length > 0 && (\r\n        <div className=\"space-y-4\">\r\n          <h3 className=\"text-lg font-semibold text-white flex items-center space-x-2\">\r\n            <Clock className=\"h-5 w-5 text-yellow-400\" />\r\n            <span>Laukia transkribavimo ({pendingMeetings.length})</span>\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            {pendingMeetings.map((meeting) => (\r\n              <div key={meeting.id} className=\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"font-medium text-white\">{meeting.title}</h4>\r\n                    <p className=\"text-sm text-white/70\">\r\n                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s\r\n                    </p>\r\n                    {meeting.transcriptionStatus.state === 'processing' && (\r\n                      <div className=\"mt-2\">\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></div>\r\n                          <span className=\"text-sm text-blue-400\">\r\n                            Transkribuojama... {meeting.transcriptionStatus.progress || 0}%\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  <button\r\n                    onClick={() => onDeleteMeeting(meeting.id)}\r\n                    className=\"p-2 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200\"\r\n                    title=\"Ištrinti pokalbį\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Completed Transcriptions */}\r\n      {completedMeetings.length > 0 && (\r\n        <div className=\"space-y-4\">\r\n          <h3 className=\"text-lg font-semibold text-white flex items-center space-x-2\">\r\n            <CheckCircle className=\"h-5 w-5 text-green-400\" />\r\n            <span>Sėkmingai transkribuota ({completedMeetings.length})</span>\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            {completedMeetings.map((meeting) => (\r\n              <div key={meeting.id} className=\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"font-medium text-white\">{meeting.title}</h4>\r\n                    <p className=\"text-sm text-white/70\">\r\n                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s\r\n                    </p>\r\n                    {meeting.transcript && (\r\n                      <p className=\"text-sm text-green-400 mt-1\">\r\n                        {meeting.transcript.length} segmentų • {meeting.participants?.length || 0} dalyvių\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <button\r\n                    onClick={() => onDeleteMeeting(meeting.id)}\r\n                    className=\"p-2 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200\"\r\n                    title=\"Ištrinti pokalbį\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Failed Transcriptions */}\r\n      {failedMeetings.length > 0 && (\r\n        <div className=\"space-y-4\">\r\n          <h3 className=\"text-lg font-semibold text-white flex items-center space-x-2\">\r\n            <XCircle className=\"h-5 w-5 text-red-400\" />\r\n            <span>Nepavyko transkribuoti ({failedMeetings.length})</span>\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            {failedMeetings.map((meeting) => (\r\n              <div key={meeting.id} className=\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"font-medium text-white\">{meeting.title}</h4>\r\n                    <p className=\"text-sm text-white/70\">\r\n                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s\r\n                    </p>\r\n                    {meeting.transcriptionStatus.error && (\r\n                      <p className=\"text-sm text-red-400 mt-1\">\r\n                        Klaida: {meeting.transcriptionStatus.error}\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                  <button\r\n                    onClick={() => onDeleteMeeting(meeting.id)}\r\n                    className=\"p-2 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200\"\r\n                    title=\"Ištrinti pokalbį\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Empty State */}\r\n      {meetings.length === 0 && (\r\n        <div className=\"flex-1 flex flex-col items-center justify-center text-center space-y-4\">\r\n          <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center\">\r\n            <Zap className=\"h-8 w-8 text-purple-400\" />\r\n          </div>\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white\">Nėra pokalbių</h3>\r\n            <p className=\"text-sm text-white/70\">\r\n              Pradėkite naują pokalbį, kad pamatytumėte jį čia\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAuBC,KAAK,EAASC,QAAQ,EAAEC,GAAG,EAAiBC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY7I,OAAO,MAAMC,oBAAyD,GAAGA,CAAC;EACxEC,QAAQ;EACRC,oBAAoB;EACpBC,qBAAqB;EACrBC,cAAc;EACdC,sBAAsB;EACtBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EAE3E,MAAMqB,cAAc,GAAIC,OAAe,IAAa;IAClD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,GAAG,GAAGF,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC;IACjC,MAAMI,aAAa,GAAGJ,IAAI,GAAG,EAAE;IAE/B,IAAIG,GAAG,GAAG,CAAC,EAAE;MACX,OAAO,GAAGA,GAAG,IAAIC,aAAa,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACP,OAAO,GAAG,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC5G;IACA,OAAO,GAAGN,IAAI,IAAI,CAACD,OAAO,GAAG,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAChE,CAAC;EAED,MAAMC,UAAU,GAAIC,IAAU,IAAa;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,WAAW,GAAG,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOH,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIJ,WAAW,GAAG,GAAG,EAAE;MAAE;MAC9B,OAAOH,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCC,OAAO,EAAE,OAAO;QAChBH,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCE,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdL,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMK,aAAa,GAAIC,MAA2B,IAAK;IACrD,QAAQA,MAAM,CAACC,KAAK;MAClB,KAAK,aAAa;QAChB,oBAAOnC,OAAA,CAACR,QAAQ;UAAC4C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,SAAS;QACZ,oBAAOxC,OAAA,CAACT,KAAK;UAAC6C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,YAAY;QACf,oBAAOxC,OAAA,CAACL,OAAO;UAACyC,SAAS,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,WAAW;QACd,oBAAOxC,OAAA,CAACN,YAAY;UAAC0C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,QAAQ;QACX,oBAAOxC,OAAA,CAACJ,OAAO;UAACwC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,WAAW;QACd,oBAAOxC,OAAA,CAACJ,OAAO;UAACwC,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAOxC,OAAA,CAACR,QAAQ;UAAC4C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,aAAa,GAAIP,MAA2B,IAAK;IACrD,QAAQA,MAAM,CAACC,KAAK;MAClB,KAAK,aAAa;QAChB,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB,KAAK,YAAY;QACf,OAAO,cAAcD,MAAM,CAACQ,QAAQ,GAAG,IAAIR,MAAM,CAACQ,QAAQ,IAAI,GAAG,EAAE,EAAE;MACvE,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,WAAWR,MAAM,CAACS,KAAK,IAAI,iBAAiB,EAAE;MACvD,KAAK,WAAW;QACd,OAAO,WAAW;MACpB;QACE,OAAO,oBAAoB;IAC/B;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAIC,OAAgB,IAAc;IAC3D,OACEA,OAAO,CAACC,SAAS,KAAKC,SAAS,IAC/BF,OAAO,CAACG,mBAAmB,CAACb,KAAK,KAAK,aAAa,IACnD,CAAC9B,cAAc;EAEnB,CAAC;EAED,MAAM4C,sBAAsB,GAAIJ,OAAgB,IAAc;IAC5D,OAAO,CACLA,OAAO,CAACG,mBAAmB,CAACb,KAAK,KAAK,YAAY,IAClDU,OAAO,CAACG,mBAAmB,CAACb,KAAK,KAAK,SAAS,KAC5C7B,sBAAsB,KAAKuC,OAAO,CAACK,EAAE;EAC5C,CAAC;EAED,MAAMC,yBAAyB,GAAIC,QAAgB,IAAa;IAC9D,MAAMC,aAAa,GAAG,MAAM,CAAC,CAAC;IAC9B,MAAMC,OAAO,GAAGF,QAAQ,GAAG,EAAE;IAC7B,MAAMG,aAAa,GAAGD,OAAO,GAAGD,aAAa;IAE7C,IAAIE,aAAa,GAAG,IAAI,EAAE;MACxB,OAAO,QAAQ;IACjB;IACA,OAAO,KAAKA,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;EACxC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGvD,QAAQ,CAACwD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACb,SAAS,CAAC;EAE3D,IAAIW,iBAAiB,CAACG,MAAM,KAAK,CAAC,EAAE;IAClC,oBACE5D,OAAA;MAAKoC,SAAS,EAAC,mIAAmI;MAAAyB,QAAA,eACtI7D,OAAA;QAAKoC,SAAS,EAAC,aAAa;QAAAyB,QAAA,gBAClC7D,OAAA;UAAKoC,SAAS,EAAC,wNAAwN;UAAAyB,QAAA,eACvO7D,OAAA,CAACR,QAAQ;YAAC4C,SAAS,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNxC,OAAA;UAAIoC,SAAS,EAAC,wCAAwC;UAAAyB,QAAA,EAAC;QAA0B;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtFxC,OAAA;UAAGoC,SAAS,EAAC,uBAAuB;UAAAyB,QAAA,EAAC;QAErC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMsB,eAAe,GAAG5D,QAAQ,CAACwD,MAAM,CAACC,CAAC,IACvCA,CAAC,CAACX,mBAAmB,CAACb,KAAK,KAAK,aAAa,IAC7CwB,CAAC,CAACX,mBAAmB,CAACb,KAAK,KAAK,SAAS,IACzCwB,CAAC,CAACX,mBAAmB,CAACb,KAAK,KAAK,YAClC,CAAC;EAED,MAAM4B,iBAAiB,GAAG7D,QAAQ,CAACwD,MAAM,CAACC,CAAC,IACzCA,CAAC,CAACX,mBAAmB,CAACb,KAAK,KAAK,WAClC,CAAC;EAED,MAAM6B,cAAc,GAAG9D,QAAQ,CAACwD,MAAM,CAACC,CAAC,IACtCA,CAAC,CAACX,mBAAmB,CAACb,KAAK,KAAK,QAClC,CAAC;EAED,oBACEnC,OAAA;IAAKoC,SAAS,EAAC,gCAAgC;IAAAyB,QAAA,GAE5CC,eAAe,CAACF,MAAM,GAAG,CAAC,iBACzB5D,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAyB,QAAA,gBACxB7D,OAAA;QAAIoC,SAAS,EAAC,8DAA8D;QAAAyB,QAAA,gBAC1E7D,OAAA,CAACT,KAAK;UAAC6C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CxC,OAAA;UAAA6D,QAAA,GAAM,yBAAuB,EAACC,eAAe,CAACF,MAAM,EAAC,GAAC;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACLxC,OAAA;QAAKoC,SAAS,EAAC,WAAW;QAAAyB,QAAA,EACvBC,eAAe,CAACG,GAAG,CAAEpB,OAAO,iBAC3B7C,OAAA;UAAsBoC,SAAS,EAAC,mEAAmE;UAAAyB,QAAA,eACjG7D,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAyB,QAAA,gBAChD7D,OAAA;cAAKoC,SAAS,EAAC,QAAQ;cAAAyB,QAAA,gBACrB7D,OAAA;gBAAIoC,SAAS,EAAC,wBAAwB;gBAAAyB,QAAA,EAAEhB,OAAO,CAACqB;cAAK;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DxC,OAAA;gBAAGoC,SAAS,EAAC,uBAAuB;gBAAAyB,QAAA,GACjChB,OAAO,CAACxB,IAAI,CAAC8C,cAAc,CAAC,OAAO,CAAC,EAAC,UAAG,EAACtB,OAAO,CAACO,QAAQ,EAAC,GAC7D;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACHK,OAAO,CAACG,mBAAmB,CAACb,KAAK,KAAK,YAAY,iBACjDnC,OAAA;gBAAKoC,SAAS,EAAC,MAAM;gBAAAyB,QAAA,eACnB7D,OAAA;kBAAKoC,SAAS,EAAC,6BAA6B;kBAAAyB,QAAA,gBAC1C7D,OAAA;oBAAKoC,SAAS,EAAC;kBAAgD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtExC,OAAA;oBAAMoC,SAAS,EAAC,uBAAuB;oBAAAyB,QAAA,GAAC,qBACnB,EAAChB,OAAO,CAACG,mBAAmB,CAACN,QAAQ,IAAI,CAAC,EAAC,GAChE;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNxC,OAAA;cACEoE,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAACsC,OAAO,CAACK,EAAE,CAAE;cAC3Cd,SAAS,EAAC,iGAAiG;cAC3G8B,KAAK,EAAC,4BAAkB;cAAAL,QAAA,eAExB7D,OAAA,CAACH,MAAM;gBAACuC,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GAzBEK,OAAO,CAACK,EAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0Bf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAuB,iBAAiB,CAACH,MAAM,GAAG,CAAC,iBAC3B5D,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAyB,QAAA,gBACxB7D,OAAA;QAAIoC,SAAS,EAAC,8DAA8D;QAAAyB,QAAA,gBAC1E7D,OAAA,CAACF,WAAW;UAACsC,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDxC,OAAA;UAAA6D,QAAA,GAAM,gCAAyB,EAACE,iBAAiB,CAACH,MAAM,EAAC,GAAC;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eACLxC,OAAA;QAAKoC,SAAS,EAAC,WAAW;QAAAyB,QAAA,EACvBE,iBAAiB,CAACE,GAAG,CAAEpB,OAAO;UAAA,IAAAwB,qBAAA;UAAA,oBAC7BrE,OAAA;YAAsBoC,SAAS,EAAC,mEAAmE;YAAAyB,QAAA,eACjG7D,OAAA;cAAKoC,SAAS,EAAC,mCAAmC;cAAAyB,QAAA,gBAChD7D,OAAA;gBAAKoC,SAAS,EAAC,QAAQ;gBAAAyB,QAAA,gBACrB7D,OAAA;kBAAIoC,SAAS,EAAC,wBAAwB;kBAAAyB,QAAA,EAAEhB,OAAO,CAACqB;gBAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3DxC,OAAA;kBAAGoC,SAAS,EAAC,uBAAuB;kBAAAyB,QAAA,GACjChB,OAAO,CAACxB,IAAI,CAAC8C,cAAc,CAAC,OAAO,CAAC,EAAC,UAAG,EAACtB,OAAO,CAACO,QAAQ,EAAC,GAC7D;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EACHK,OAAO,CAACyB,UAAU,iBACjBtE,OAAA;kBAAGoC,SAAS,EAAC,6BAA6B;kBAAAyB,QAAA,GACvChB,OAAO,CAACyB,UAAU,CAACV,MAAM,EAAC,wBAAY,EAAC,EAAAS,qBAAA,GAAAxB,OAAO,CAAC0B,YAAY,cAAAF,qBAAA,uBAApBA,qBAAA,CAAsBT,MAAM,KAAI,CAAC,EAAC,eAC5E;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNxC,OAAA;gBACEoE,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAACsC,OAAO,CAACK,EAAE,CAAE;gBAC3Cd,SAAS,EAAC,iGAAiG;gBAC3G8B,KAAK,EAAC,4BAAkB;gBAAAL,QAAA,eAExB7D,OAAA,CAACH,MAAM;kBAACuC,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC,GApBEK,OAAO,CAACK,EAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBf,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAwB,cAAc,CAACJ,MAAM,GAAG,CAAC,iBACxB5D,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAyB,QAAA,gBACxB7D,OAAA;QAAIoC,SAAS,EAAC,8DAA8D;QAAAyB,QAAA,gBAC1E7D,OAAA,CAACJ,OAAO;UAACwC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CxC,OAAA;UAAA6D,QAAA,GAAM,0BAAwB,EAACG,cAAc,CAACJ,MAAM,EAAC,GAAC;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACLxC,OAAA;QAAKoC,SAAS,EAAC,WAAW;QAAAyB,QAAA,EACvBG,cAAc,CAACC,GAAG,CAAEpB,OAAO,iBAC1B7C,OAAA;UAAsBoC,SAAS,EAAC,mEAAmE;UAAAyB,QAAA,eACjG7D,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAyB,QAAA,gBAChD7D,OAAA;cAAKoC,SAAS,EAAC,QAAQ;cAAAyB,QAAA,gBACrB7D,OAAA;gBAAIoC,SAAS,EAAC,wBAAwB;gBAAAyB,QAAA,EAAEhB,OAAO,CAACqB;cAAK;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3DxC,OAAA;gBAAGoC,SAAS,EAAC,uBAAuB;gBAAAyB,QAAA,GACjChB,OAAO,CAACxB,IAAI,CAAC8C,cAAc,CAAC,OAAO,CAAC,EAAC,UAAG,EAACtB,OAAO,CAACO,QAAQ,EAAC,GAC7D;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACHK,OAAO,CAACG,mBAAmB,CAACL,KAAK,iBAChC3C,OAAA;gBAAGoC,SAAS,EAAC,2BAA2B;gBAAAyB,QAAA,GAAC,UAC/B,EAAChB,OAAO,CAACG,mBAAmB,CAACL,KAAK;cAAA;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNxC,OAAA;cACEoE,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAACsC,OAAO,CAACK,EAAE,CAAE;cAC3Cd,SAAS,EAAC,iGAAiG;cAC3G8B,KAAK,EAAC,4BAAkB;cAAAL,QAAA,eAExB7D,OAAA,CAACH,MAAM;gBAACuC,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GApBEK,OAAO,CAACK,EAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAtC,QAAQ,CAAC0D,MAAM,KAAK,CAAC,iBACpB5D,OAAA;MAAKoC,SAAS,EAAC,wEAAwE;MAAAyB,QAAA,gBACrF7D,OAAA;QAAKoC,SAAS,EAAC,6GAA6G;QAAAyB,QAAA,eAC1H7D,OAAA,CAACP,GAAG;UAAC2C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACNxC,OAAA;QAAA6D,QAAA,gBACE7D,OAAA;UAAIoC,SAAS,EAAC,kCAAkC;UAAAyB,QAAA,EAAC;QAAa;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnExC,OAAA;UAAGoC,SAAS,EAAC,uBAAuB;UAAAyB,QAAA,EAAC;QAErC;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChC,EAAA,CAlRWP,oBAAyD;AAAAuE,EAAA,GAAzDvE,oBAAyD;AAAA,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}