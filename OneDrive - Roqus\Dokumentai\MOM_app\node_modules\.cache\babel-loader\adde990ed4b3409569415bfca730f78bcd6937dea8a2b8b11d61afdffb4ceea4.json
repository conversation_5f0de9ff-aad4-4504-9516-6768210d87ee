{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\CollapsibleTranscriptsList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { ChevronDown, ChevronRight, Calendar, Clock, Users, FileText, CheckCircle2, AlertCircle, Loader2, Download, Volume2 } from 'lucide-react';\nimport { ProfessionalTranscriptViewer } from './ProfessionalTranscriptViewer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CollapsibleTranscriptsList = ({\n  meetings,\n  onEditSegment,\n  onExportMeeting\n}) => {\n  _s();\n  const [expandedMeeting, setExpandedMeeting] = useState(null);\n  const formatDuration = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const hrs = Math.floor(mins / 60);\n    const remainingMins = mins % 60;\n    if (hrs > 0) {\n      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\n    }\n    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;\n  };\n  const formatDate = date => {\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      return `Šiandien, ${date.toLocaleTimeString('lt-LT', {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } else if (diffInHours < 48) {\n      return `Vakar, ${date.toLocaleTimeString('lt-LT', {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } else if (diffInHours < 168) {\n      // 7 dienos\n      return date.toLocaleDateString('lt-LT', {\n        weekday: 'long',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('lt-LT', {\n        month: 'long',\n        day: 'numeric',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n  const getStatusIcon = meeting => {\n    const status = meeting.transcriptionStatus.state;\n    switch (status) {\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(CheckCircle2, {\n          className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 16\n        }, this);\n      case 'processing':\n        return /*#__PURE__*/_jsxDEV(Loader2, {\n          className: \"h-4 w-4 text-blue-500 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-4 w-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 16\n        }, this);\n      case 'pending':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-4 w-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusText = meeting => {\n    const status = meeting.transcriptionStatus.state;\n    switch (status) {\n      case 'completed':\n        return 'Baigtas';\n      case 'processing':\n        return `Apdorojama${meeting.transcriptionStatus.progress ? ` (${meeting.transcriptionStatus.progress}%)` : ''}`;\n      case 'failed':\n        return 'Nepavyko';\n      case 'pending':\n        return 'Eilėje';\n      case 'not_started':\n        return meeting.audioBlob ? 'Galima transkribuoti' : 'Nėra audio';\n      default:\n        return 'Nežinomas statusas';\n    }\n  };\n  const getSpeakersSummary = meeting => {\n    if (!meeting.participants || meeting.participants.length === 0) {\n      return 'Kalbėtojai neidentifikuoti';\n    }\n    if (meeting.participants.length <= 2) {\n      return meeting.participants.map(p => p.name).join(', ');\n    }\n    return `${meeting.participants[0].name} ir ${meeting.participants.length - 1} kiti`;\n  };\n  const getTranscriptStats = meeting => {\n    if (!meeting.transcript || meeting.transcript.length === 0) {\n      return {\n        segments: 0,\n        words: 0,\n        confidence: 0\n      };\n    }\n    const segments = meeting.transcript.length;\n    const words = meeting.transcript.reduce((sum, seg) => sum + (seg.wordsCount || seg.text.split(' ').length), 0);\n    const confidence = meeting.transcript.reduce((sum, seg) => sum + (seg.confidence || 0), 0) / segments;\n    return {\n      segments,\n      words,\n      confidence: Math.round(confidence * 100)\n    };\n  };\n  const toggleExpanded = meetingId => {\n    setExpandedMeeting(expandedMeeting === meetingId ? null : meetingId);\n  };\n  const hasTranscript = meeting => {\n    return meeting.transcriptionStatus.state === 'completed' && meeting.transcript && meeting.transcript.length > 0;\n  };\n\n  // Filter meetings that have been completed (audio recorded)\n  const completedMeetings = meetings.filter(meeting => meeting.status === 'completed' || meeting.status === 'processing').sort((a, b) => b.date.getTime() - a.date.getTime());\n  if (completedMeetings.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-border-fade rounded-3xl p-12 text-center shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mx-auto mb-6 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\",\n          children: /*#__PURE__*/_jsxDEV(FileText, {\n            className: \"h-10 w-10 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-3\",\n          children: \"N\\u0117ra pokalbi\\u0173\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 max-w-md mx-auto mb-6\",\n          children: \"Sukurkite ir u\\u017Ebaikite pokalbio \\u012Fra\\u0161ym\\u0105, kad pamatytum\\u0117te transkribavimo rezultatus \\u010Dia.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-3 justify-center items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83C\\uDFA4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u012Era\\u0161ykite pokalb\\u012F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Transkribuokite su Whisper\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Per\\u017Ei\\u016Br\\u0117kite rezultatus\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [completedMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gradient-border-fade rounded-3xl p-6 shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary hover-gradient-shift float-effect\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Pokalbi\\u0173 ap\\u017Evalga\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: [\"Atnaujinta: \", new Date().toLocaleTimeString('lt-LT', {\n            hour: '2-digit',\n            minute: '2-digit'\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 lg:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-blue-600 mb-1\",\n            children: completedMeetings.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: completedMeetings.length === 1 ? 'Pokalbis' : 'Pokalbiai'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-purple-600 mb-1\",\n            children: completedMeetings.filter(m => hasTranscript(m)).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: completedMeetings.filter(m => hasTranscript(m)).length === 1 ? 'Transkribuotas' : 'Transkribuoti'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-green-600 mb-1\",\n            children: [Math.round(completedMeetings.reduce((sum, m) => sum + m.duration, 0) / 60), \"min\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: \"Bendra trukm\\u0117\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-bold text-orange-600 mb-1\",\n            children: completedMeetings.filter(m => hasTranscript(m)).reduce((sum, m) => sum + getTranscriptStats(m).words, 0).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm font-medium text-gray-700\",\n            children: completedMeetings.filter(m => hasTranscript(m)).reduce((sum, m) => sum + getTranscriptStats(m).words, 0) === 1 ? 'Žodis' : 'Žodžiai'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-4 pt-4 border-t border-white/40\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600\",\n            children: \"Transkribavimo progresas:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium text-gray-800\",\n            children: completedMeetings.length > 0 ? `${Math.round(completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length * 100)}%` : '0%'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 w-full bg-white/40 rounded-full h-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500\",\n            style: {\n              width: completedMeetings.length > 0 ? `${completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length * 100}%` : '0%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 18\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: [\"Pokalbi\\u0173 istorija\", completedMeetings.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-normal text-gray-500 ml-2\",\n            children: [\"(\", completedMeetings.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: \"R\\u016B\\u0161iuoti pagal dat\\u0105 (naujausi pirma)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), completedMeetings.map(meeting => {\n        const stats = getTranscriptStats(meeting);\n        const isExpanded = expandedMeeting === meeting.id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `gradient-border-fade rounded-3xl overflow-hidden transition-ultra hover-gradient-shift ${isExpanded ? 'shadow-primary bg-unique-gradient-2 scale-[1.02] pulse-subtle' : 'shadow-soft bg-unique-gradient-3 hover:shadow-elegant hover:scale-[1.01] float-effect'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `p-6 cursor-pointer transition-smooth ${isExpanded ? 'bg-gradient-to-r from-blue-50/40 via-purple-50/30 to-indigo-50/40 border-b border-gradient-fade' : 'hover:bg-gradient-to-r hover:from-white/50 hover:via-blue-50/30 hover:to-white/50'}`,\n            onClick: () => toggleExpanded(meeting.id),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4 flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors\",\n                  children: isExpanded ? /*#__PURE__*/_jsxDEV(ChevronDown, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(ChevronRight, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-gray-900 truncate\",\n                      children: meeting.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 306,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [getStatusIcon(meeting), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-600\",\n                        children: getStatusText(meeting)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 311,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: formatDate(meeting.date)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Clock, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 324,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: formatDuration(meeting.duration)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 325,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 329,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: getSpeakersSummary(meeting)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 328,\n                      columnNumber: 23\n                    }, this), hasTranscript(meeting) && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FileText, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [stats.words.toLocaleString(), \" \\u017Eod\\u017Eiai (\", stats.confidence, \"% tikslumas)\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 336,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 42\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 flex-shrink-0\",\n                children: [meeting.audioBlob && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: e => {\n                    e.stopPropagation();\n                    // Play audio functionality could be added here\n                  },\n                  className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\",\n                  title: \"Klausyti audio\",\n                  children: /*#__PURE__*/_jsxDEV(Volume2, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: e => {\n                    e.stopPropagation();\n                    onExportMeeting(meeting);\n                  },\n                  className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\",\n                  title: \"Eksportuoti\",\n                  children: /*#__PURE__*/_jsxDEV(Download, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 26\n          }, this), isExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-fadeIn\",\n            style: {\n              animation: 'fadeIn 0.3s ease-in-out'\n            },\n            children: hasTranscript(meeting) ? /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n              meetings: [meeting],\n              onDeleteMeeting: () => {}\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-8 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                children: meeting.transcriptionStatus.state === 'processing' ? /*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"h-6 w-6 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 25\n                }, this) : meeting.transcriptionStatus.state === 'failed' ? /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"h-6 w-6 text-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"h-6 w-6 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-medium text-gray-900 mb-2\",\n                children: meeting.transcriptionStatus.state === 'processing' ? 'Apdorojama transkribavimas' : meeting.transcriptionStatus.state === 'failed' ? 'Transkribavimas nepavyko' : 'Nėra transkribavimo'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: meeting.transcriptionStatus.state === 'processing' ? `Prašome palaukti... ${meeting.transcriptionStatus.progress || 0}%` : meeting.transcriptionStatus.state === 'failed' ? meeting.transcriptionStatus.error || 'Nežinoma klaida' : meeting.audioBlob ? 'Eikite į transkribavimo skyrių, kad pradėtumėte' : 'Šis pokalbis neturi audio įrašo'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 21\n              }, this), meeting.transcriptionStatus.state === 'processing' && meeting.transcriptionStatus.progress && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 w-64 bg-gray-200 rounded-full h-2 mx-auto\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                  style: {\n                    width: `${meeting.transcriptionStatus.progress}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 16\n          }, this)]\n        }, meeting.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 22\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_s(CollapsibleTranscriptsList, \"xl+1qxd3RRxKYfNdGPK+7eGKlQc=\");\n_c = CollapsibleTranscriptsList;\nvar _c;\n$RefreshReg$(_c, \"CollapsibleTranscriptsList\");", "map": {"version": 3, "names": ["React", "useState", "ChevronDown", "ChevronRight", "Calendar", "Clock", "Users", "FileText", "CheckCircle2", "AlertCircle", "Loader2", "Download", "Volume2", "ProfessionalTranscriptViewer", "jsxDEV", "_jsxDEV", "CollapsibleTranscriptsList", "meetings", "onEditSegment", "onExportMeeting", "_s", "expandedMeeting", "setExpandedMeeting", "formatDuration", "seconds", "mins", "Math", "floor", "hrs", "remainingMins", "toString", "padStart", "formatDate", "date", "now", "Date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "month", "day", "year", "getStatusIcon", "meeting", "status", "transcriptionStatus", "state", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusText", "progress", "audioBlob", "getSpeakersSummary", "participants", "length", "map", "p", "name", "join", "getTranscriptStats", "transcript", "segments", "words", "confidence", "reduce", "sum", "seg", "wordsCount", "text", "split", "round", "toggleExpanded", "meetingId", "hasTranscript", "completedMeetings", "filter", "sort", "a", "b", "children", "m", "duration", "toLocaleString", "style", "width", "stats", "isExpanded", "id", "onClick", "title", "e", "stopPropagation", "animation", "onDeleteMeeting", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CollapsibleTranscriptsList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { ChevronDown, ChevronRight, Calendar, Clock, Users, FileText, CheckCircle2, AlertCircle, Loader2, Download, Edit3, Play, Volume2 } from 'lucide-react';\r\nimport { Meeting, Speaker } from '../types/meeting';\r\nimport { ProfessionalTranscriptViewer } from './ProfessionalTranscriptViewer';\r\n\r\ninterface CollapsibleTranscriptsListProps {\r\n  meetings: Meeting[];\r\n  onEditSegment?: (meetingId: string, segmentId: any, newText: any) => void;\r\n  onExportMeeting: (meeting: Meeting) => void;\r\n}\r\n\r\nexport const CollapsibleTranscriptsList: React.FC<CollapsibleTranscriptsListProps> = ({\r\n  meetings,\r\n  onEditSegment,\r\n  onExportMeeting,\r\n}) => {\r\n  const [expandedMeeting, setExpandedMeeting] = useState<string | null>(null);\r\n\r\n  const formatDuration = (seconds: number): string => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const hrs = Math.floor(mins / 60);\r\n    const remainingMins = mins % 60;\r\n    \r\n    if (hrs > 0) {\r\n      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n    }\r\n    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const formatDate = (date: Date): string => {\r\n    const now = new Date();\r\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\r\n    \r\n    if (diffInHours < 24) {\r\n      return `Šiandien, ${date.toLocaleTimeString('lt-LT', { \r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      })}`;\r\n    } else if (diffInHours < 48) {\r\n      return `Vakar, ${date.toLocaleTimeString('lt-LT', { \r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      })}`;\r\n    } else if (diffInHours < 168) { // 7 dienos\r\n      return date.toLocaleDateString('lt-LT', { \r\n        weekday: 'long',\r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    } else {\r\n      return date.toLocaleDateString('lt-LT', { \r\n        month: 'long', \r\n        day: 'numeric',\r\n        year: 'numeric',\r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (meeting: Meeting) => {\r\n    const status = meeting.transcriptionStatus.state;\r\n    \r\n    switch (status) {\r\n      case 'completed':\r\n        return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n      case 'processing':\r\n        return <Loader2 className=\"h-4 w-4 text-blue-500 animate-spin\" />;\r\n      case 'failed':\r\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\r\n      case 'pending':\r\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />;\r\n      default:\r\n        return <FileText className=\"h-4 w-4 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusText = (meeting: Meeting) => {\r\n    const status = meeting.transcriptionStatus.state;\r\n    \r\n    switch (status) {\r\n      case 'completed':\r\n        return 'Baigtas';\r\n      case 'processing':\r\n        return `Apdorojama${meeting.transcriptionStatus.progress ? ` (${meeting.transcriptionStatus.progress}%)` : ''}`;\r\n      case 'failed':\r\n        return 'Nepavyko';\r\n      case 'pending':\r\n        return 'Eilėje';\r\n      case 'not_started':\r\n        return meeting.audioBlob ? 'Galima transkribuoti' : 'Nėra audio';\r\n      default:\r\n        return 'Nežinomas statusas';\r\n    }\r\n  };\r\n\r\n  const getSpeakersSummary = (meeting: Meeting) => {\r\n    if (!meeting.participants || meeting.participants.length === 0) {\r\n      return 'Kalbėtojai neidentifikuoti';\r\n    }\r\n    \r\n    if (meeting.participants.length <= 2) {\r\n      return meeting.participants.map(p => p.name).join(', ');\r\n    }\r\n    \r\n    return `${meeting.participants[0].name} ir ${meeting.participants.length - 1} kiti`;\r\n  };\r\n\r\n  const getTranscriptStats = (meeting: Meeting) => {\r\n    if (!meeting.transcript || meeting.transcript.length === 0) {\r\n      return { segments: 0, words: 0, confidence: 0 };\r\n    }\r\n    \r\n    const segments = meeting.transcript.length;\r\n    const words = meeting.transcript.reduce((sum, seg) => \r\n      sum + (seg.wordsCount || seg.text.split(' ').length), 0);\r\n    const confidence = meeting.transcript.reduce((sum, seg) => \r\n      sum + (seg.confidence || 0), 0) / segments;\r\n    \r\n    return { segments, words, confidence: Math.round(confidence * 100) };\r\n  };\r\n\r\n  const toggleExpanded = (meetingId: string) => {\r\n    setExpandedMeeting(expandedMeeting === meetingId ? null : meetingId);\r\n  };\r\n\r\n  const hasTranscript = (meeting: Meeting) => {\r\n    return meeting.transcriptionStatus.state === 'completed' && \r\n           meeting.transcript && \r\n           meeting.transcript.length > 0;\r\n  };\r\n\r\n  // Filter meetings that have been completed (audio recorded)\r\n  const completedMeetings = meetings.filter(meeting => \r\n    meeting.status === 'completed' || meeting.status === 'processing'\r\n  ).sort((a, b) => b.date.getTime() - a.date.getTime());\r\n\r\n  if (completedMeetings.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        {/* Empty State with Better Styling */}\r\n        <div className=\"gradient-border-fade rounded-3xl p-12 text-center shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary\">\r\n          <div className=\"w-20 h-20 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mx-auto mb-6 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\">\r\n            <FileText className=\"h-10 w-10 text-blue-500\" />\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">Nėra pokalbių</h2>\r\n          <p className=\"text-gray-600 max-w-md mx-auto mb-6\">\r\n            Sukurkite ir užbaikite pokalbio įrašymą, kad pamatytumėte transkribavimo rezultatus čia.\r\n          </p>\r\n          \r\n          <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\r\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\">\r\n              <span>🎤</span>\r\n              <span>Įrašykite pokalbį</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\">\r\n              <span>⚡</span>\r\n              <span>Transkribuokite su Whisper</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\">\r\n              <span>📊</span>\r\n              <span>Peržiūrėkite rezultatus</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Summary Stats - Moved to Top */}\r\n      {completedMeetings.length > 0 && (\r\n                 <div className=\"gradient-border-fade rounded-3xl p-6 shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary hover-gradient-shift float-effect\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900\">Pokalbių apžvalga</h2>\r\n            <div className=\"text-sm text-gray-500\">\r\n              Atnaujinta: {new Date().toLocaleTimeString('lt-LT', { \r\n                hour: '2-digit', \r\n                minute: '2-digit' \r\n              })}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-blue-600 mb-1\">\r\n                {completedMeetings.length}\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">\r\n                {completedMeetings.length === 1 ? 'Pokalbis' : 'Pokalbiai'}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-purple-600 mb-1\">\r\n                {completedMeetings.filter(m => hasTranscript(m)).length}\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">\r\n                {completedMeetings.filter(m => hasTranscript(m)).length === 1 ? 'Transkribuotas' : 'Transkribuoti'}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-green-600 mb-1\">\r\n                {Math.round(\r\n                  completedMeetings.reduce((sum, m) => sum + m.duration, 0) / 60\r\n                )}min\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">Bendra trukmė</div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-orange-600 mb-1\">\r\n                {completedMeetings\r\n                  .filter(m => hasTranscript(m))\r\n                  .reduce((sum, m) => sum + getTranscriptStats(m).words, 0)\r\n                  .toLocaleString()\r\n                }\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">\r\n                {completedMeetings\r\n                  .filter(m => hasTranscript(m))\r\n                  .reduce((sum, m) => sum + getTranscriptStats(m).words, 0) === 1 ? 'Žodis' : 'Žodžiai'}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Progress Overview */}\r\n          <div className=\"mt-4 pt-4 border-t border-white/40\">\r\n            <div className=\"flex items-center justify-between text-sm\">\r\n              <span className=\"text-gray-600\">Transkribavimo progresas:</span>\r\n              <span className=\"font-medium text-gray-800\">\r\n                {completedMeetings.length > 0 \r\n                  ? `${Math.round((completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length) * 100)}%`\r\n                  : '0%'\r\n                }\r\n              </span>\r\n            </div>\r\n            <div className=\"mt-2 w-full bg-white/40 rounded-full h-2\">\r\n              <div \r\n                className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500\"\r\n                style={{ \r\n                  width: completedMeetings.length > 0 \r\n                    ? `${(completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length) * 100}%`\r\n                    : '0%'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Meetings List */}\r\n      <div className=\"space-y-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900\">\r\n            Pokalbių istorija\r\n            {completedMeetings.length > 0 && (\r\n              <span className=\"text-sm font-normal text-gray-500 ml-2\">\r\n                ({completedMeetings.length})\r\n              </span>\r\n            )}\r\n          </h3>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Rūšiuoti pagal datą (naujausi pirma)\r\n          </div>\r\n        </div>\r\n      \r\n        {completedMeetings.map((meeting) => {\r\n          const stats = getTranscriptStats(meeting);\r\n          const isExpanded = expandedMeeting === meeting.id;\r\n          \r\n          return (\r\n                     <div\r\n             key={meeting.id}\r\n             className={`gradient-border-fade rounded-3xl overflow-hidden transition-ultra hover-gradient-shift ${\r\n               isExpanded \r\n                 ? 'shadow-primary bg-unique-gradient-2 scale-[1.02] pulse-subtle' \r\n                 : 'shadow-soft bg-unique-gradient-3 hover:shadow-elegant hover:scale-[1.01] float-effect'\r\n             }`}\r\n           >\r\n            {/* Meeting Header - Always Visible */}\r\n                         <div \r\n               className={`p-6 cursor-pointer transition-smooth ${\r\n                 isExpanded \r\n                   ? 'bg-gradient-to-r from-blue-50/40 via-purple-50/30 to-indigo-50/40 border-b border-gradient-fade' \r\n                   : 'hover:bg-gradient-to-r hover:from-white/50 hover:via-blue-50/30 hover:to-white/50'\r\n               }`}\r\n               onClick={() => toggleExpanded(meeting.id)}\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4 flex-1 min-w-0\">\r\n                  {/* Expand/Collapse Button */}\r\n                  <button className=\"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors\">\r\n                    {isExpanded ? (\r\n                      <ChevronDown className=\"h-5 w-5\" />\r\n                    ) : (\r\n                      <ChevronRight className=\"h-5 w-5\" />\r\n                    )}\r\n                  </button>\r\n                  \r\n                  {/* Meeting Info */}\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <div className=\"flex items-center space-x-3 mb-2\">\r\n                      <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\r\n                        {meeting.title}\r\n                      </h3>\r\n                      <div className=\"flex items-center space-x-1\">\r\n                        {getStatusIcon(meeting)}\r\n                        <span className=\"text-sm text-gray-600\">\r\n                          {getStatusText(meeting)}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                                         <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-500\">\r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Calendar className=\"h-4 w-4\" />\r\n                        <span>{formatDate(meeting.date)}</span>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Clock className=\"h-4 w-4\" />\r\n                        <span>{formatDuration(meeting.duration)}</span>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Users className=\"h-4 w-4\" />\r\n                        <span>{getSpeakersSummary(meeting)}</span>\r\n                      </div>\r\n                      \r\n                      {hasTranscript(meeting) && (\r\n                        <div className=\"flex items-center space-x-1\">\r\n                          <FileText className=\"h-4 w-4\" />\r\n                          <span>\r\n                            {stats.words.toLocaleString()} žodžiai \r\n                            ({stats.confidence}% tikslumas)\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Quick Actions */}\r\n                <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n                  {meeting.audioBlob && (\r\n                    <button \r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        // Play audio functionality could be added here\r\n                      }}\r\n                      className=\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\"\r\n                      title=\"Klausyti audio\"\r\n                    >\r\n                      <Volume2 className=\"h-4 w-4\" />\r\n                    </button>\r\n                  )}\r\n                  \r\n                  <button \r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      onExportMeeting(meeting);\r\n                    }}\r\n                    className=\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\"\r\n                    title=\"Eksportuoti\"\r\n                  >\r\n                    <Download className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n                         {/* Expanded Content - Transcript Viewer */}\r\n             {isExpanded && (\r\n               <div className=\"animate-fadeIn\"\r\n                    style={{\r\n                      animation: 'fadeIn 0.3s ease-in-out'\r\n                    }}\r\n               >\r\n                {hasTranscript(meeting) ? (\r\n                  <ProfessionalTranscriptViewer\r\n                    meetings={[meeting]}\r\n                    onDeleteMeeting={() => {}}\r\n                  />\r\n                ) : (\r\n                  <div className=\"p-8 text-center\">\r\n                    <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\">\r\n                      {meeting.transcriptionStatus.state === 'processing' ? (\r\n                        <Loader2 className=\"h-6 w-6 text-blue-500 animate-spin\" />\r\n                      ) : meeting.transcriptionStatus.state === 'failed' ? (\r\n                        <AlertCircle className=\"h-6 w-6 text-red-500\" />\r\n                      ) : (\r\n                        <FileText className=\"h-6 w-6 text-gray-400\" />\r\n                      )}\r\n                    </div>\r\n                    \r\n                    <h4 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n                      {meeting.transcriptionStatus.state === 'processing' \r\n                        ? 'Apdorojama transkribavimas'\r\n                        : meeting.transcriptionStatus.state === 'failed'\r\n                        ? 'Transkribavimas nepavyko'\r\n                        : 'Nėra transkribavimo'\r\n                      }\r\n                    </h4>\r\n                    \r\n                    <p className=\"text-sm text-gray-500\">\r\n                      {meeting.transcriptionStatus.state === 'processing' \r\n                        ? `Prašome palaukti... ${meeting.transcriptionStatus.progress || 0}%`\r\n                        : meeting.transcriptionStatus.state === 'failed'\r\n                        ? meeting.transcriptionStatus.error || 'Nežinoma klaida'\r\n                        : meeting.audioBlob\r\n                        ? 'Eikite į transkribavimo skyrių, kad pradėtumėte'\r\n                        : 'Šis pokalbis neturi audio įrašo'\r\n                      }\r\n                    </p>\r\n                    \r\n                    {meeting.transcriptionStatus.state === 'processing' && \r\n                     meeting.transcriptionStatus.progress && (\r\n                      <div className=\"mt-4 w-64 bg-gray-200 rounded-full h-2 mx-auto\">\r\n                        <div \r\n                          className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                          style={{ width: `${meeting.transcriptionStatus.progress}%` }}\r\n                        />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,WAAW,EAAEC,OAAO,EAAEC,QAAQ,EAAeC,OAAO,QAAQ,cAAc;AAE9J,SAASC,4BAA4B,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ9E,OAAO,MAAMC,0BAAqE,GAAGA,CAAC;EACpFC,QAAQ;EACRC,aAAa;EACbC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAgB,IAAI,CAAC;EAE3E,MAAMsB,cAAc,GAAIC,OAAe,IAAa;IAClD,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,GAAG,GAAGF,IAAI,CAACC,KAAK,CAACF,IAAI,GAAG,EAAE,CAAC;IACjC,MAAMI,aAAa,GAAGJ,IAAI,GAAG,EAAE;IAE/B,IAAIG,GAAG,GAAG,CAAC,EAAE;MACX,OAAO,GAAGA,GAAG,IAAIC,aAAa,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAACP,OAAO,GAAG,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC5G;IACA,OAAO,GAAGN,IAAI,IAAI,CAACD,OAAO,GAAG,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAChE,CAAC;EAED,MAAMC,UAAU,GAAIC,IAAU,IAAa;IACzC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMC,WAAW,GAAG,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAACI,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,EAAE,EAAE;MACpB,OAAO,aAAaH,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;QACnDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC,EAAE;IACN,CAAC,MAAM,IAAIJ,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,UAAUH,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;QAChDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC,EAAE;IACN,CAAC,MAAM,IAAIJ,WAAW,GAAG,GAAG,EAAE;MAAE;MAC9B,OAAOH,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCC,OAAO,EAAE,MAAM;QACfH,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCE,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfN,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMM,aAAa,GAAIC,OAAgB,IAAK;IAC1C,MAAMC,MAAM,GAAGD,OAAO,CAACE,mBAAmB,CAACC,KAAK;IAEhD,QAAQF,MAAM;MACZ,KAAK,WAAW;QACd,oBAAOjC,OAAA,CAACP,YAAY;UAAC2C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,YAAY;QACf,oBAAOxC,OAAA,CAACL,OAAO;UAACyC,SAAS,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,QAAQ;QACX,oBAAOxC,OAAA,CAACN,WAAW;UAAC0C,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,SAAS;QACZ,oBAAOxC,OAAA,CAACV,KAAK;UAAC8C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,oBAAOxC,OAAA,CAACR,QAAQ;UAAC4C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzD;EACF,CAAC;EAED,MAAMC,aAAa,GAAIT,OAAgB,IAAK;IAC1C,MAAMC,MAAM,GAAGD,OAAO,CAACE,mBAAmB,CAACC,KAAK;IAEhD,QAAQF,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,aAAaD,OAAO,CAACE,mBAAmB,CAACQ,QAAQ,GAAG,KAAKV,OAAO,CAACE,mBAAmB,CAACQ,QAAQ,IAAI,GAAG,EAAE,EAAE;MACjH,KAAK,QAAQ;QACX,OAAO,UAAU;MACnB,KAAK,SAAS;QACZ,OAAO,QAAQ;MACjB,KAAK,aAAa;QAChB,OAAOV,OAAO,CAACW,SAAS,GAAG,sBAAsB,GAAG,YAAY;MAClE;QACE,OAAO,oBAAoB;IAC/B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIZ,OAAgB,IAAK;IAC/C,IAAI,CAACA,OAAO,CAACa,YAAY,IAAIb,OAAO,CAACa,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9D,OAAO,4BAA4B;IACrC;IAEA,IAAId,OAAO,CAACa,YAAY,CAACC,MAAM,IAAI,CAAC,EAAE;MACpC,OAAOd,OAAO,CAACa,YAAY,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACzD;IAEA,OAAO,GAAGlB,OAAO,CAACa,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,OAAOjB,OAAO,CAACa,YAAY,CAACC,MAAM,GAAG,CAAC,OAAO;EACrF,CAAC;EAED,MAAMK,kBAAkB,GAAInB,OAAgB,IAAK;IAC/C,IAAI,CAACA,OAAO,CAACoB,UAAU,IAAIpB,OAAO,CAACoB,UAAU,CAACN,MAAM,KAAK,CAAC,EAAE;MAC1D,OAAO;QAAEO,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC;IACjD;IAEA,MAAMF,QAAQ,GAAGrB,OAAO,CAACoB,UAAU,CAACN,MAAM;IAC1C,MAAMQ,KAAK,GAAGtB,OAAO,CAACoB,UAAU,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAC/CD,GAAG,IAAIC,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACf,MAAM,CAAC,EAAE,CAAC,CAAC;IAC1D,MAAMS,UAAU,GAAGvB,OAAO,CAACoB,UAAU,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KACpDD,GAAG,IAAIC,GAAG,CAACH,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGF,QAAQ;IAE5C,OAAO;MAAEA,QAAQ;MAAEC,KAAK;MAAEC,UAAU,EAAE5C,IAAI,CAACmD,KAAK,CAACP,UAAU,GAAG,GAAG;IAAE,CAAC;EACtE,CAAC;EAED,MAAMQ,cAAc,GAAIC,SAAiB,IAAK;IAC5CzD,kBAAkB,CAACD,eAAe,KAAK0D,SAAS,GAAG,IAAI,GAAGA,SAAS,CAAC;EACtE,CAAC;EAED,MAAMC,aAAa,GAAIjC,OAAgB,IAAK;IAC1C,OAAOA,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,WAAW,IACjDH,OAAO,CAACoB,UAAU,IAClBpB,OAAO,CAACoB,UAAU,CAACN,MAAM,GAAG,CAAC;EACtC,CAAC;;EAED;EACA,MAAMoB,iBAAiB,GAAGhE,QAAQ,CAACiE,MAAM,CAACnC,OAAO,IAC/CA,OAAO,CAACC,MAAM,KAAK,WAAW,IAAID,OAAO,CAACC,MAAM,KAAK,YACvD,CAAC,CAACmC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACpD,IAAI,CAACI,OAAO,CAAC,CAAC,GAAG+C,CAAC,CAACnD,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC;EAErD,IAAI4C,iBAAiB,CAACpB,MAAM,KAAK,CAAC,EAAE;IAClC,oBACE9C,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAmC,QAAA,eAExBvE,OAAA;QAAKoC,SAAS,EAAC,gJAAgJ;QAAAmC,QAAA,gBAC7JvE,OAAA;UAAKoC,SAAS,EAAC,wNAAwN;UAAAmC,QAAA,eACrOvE,OAAA,CAACR,QAAQ;YAAC4C,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNxC,OAAA;UAAIoC,SAAS,EAAC,0CAA0C;UAAAmC,QAAA,EAAC;QAAa;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ExC,OAAA;UAAGoC,SAAS,EAAC,qCAAqC;UAAAmC,QAAA,EAAC;QAEnD;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJxC,OAAA;UAAKoC,SAAS,EAAC,6DAA6D;UAAAmC,QAAA,gBAC1EvE,OAAA;YAAKoC,SAAS,EAAC,wFAAwF;YAAAmC,QAAA,gBACrGvE,OAAA;cAAAuE,QAAA,EAAM;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfxC,OAAA;cAAAuE,QAAA,EAAM;YAAiB;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNxC,OAAA;YAAKoC,SAAS,EAAC,wFAAwF;YAAAmC,QAAA,gBACrGvE,OAAA;cAAAuE,QAAA,EAAM;YAAC;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdxC,OAAA;cAAAuE,QAAA,EAAM;YAA0B;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNxC,OAAA;YAAKoC,SAAS,EAAC,wFAAwF;YAAAmC,QAAA,gBACrGvE,OAAA;cAAAuE,QAAA,EAAM;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACfxC,OAAA;cAAAuE,QAAA,EAAM;YAAuB;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExC,OAAA;IAAKoC,SAAS,EAAC,WAAW;IAAAmC,QAAA,GAEvBL,iBAAiB,CAACpB,MAAM,GAAG,CAAC,iBAClB9C,OAAA;MAAKoC,SAAS,EAAC,qKAAqK;MAAAmC,QAAA,gBAC3LvE,OAAA;QAAKoC,SAAS,EAAC,wCAAwC;QAAAmC,QAAA,gBACrDvE,OAAA;UAAIoC,SAAS,EAAC,qCAAqC;UAAAmC,QAAA,EAAC;QAAiB;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1ExC,OAAA;UAAKoC,SAAS,EAAC,uBAAuB;UAAAmC,QAAA,GAAC,cACzB,EAAC,IAAInD,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC,OAAO,EAAE;YAClDC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE;UACV,CAAC,CAAC;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxC,OAAA;QAAKoC,SAAS,EAAC,uCAAuC;QAAAmC,QAAA,gBACpDvE,OAAA;UAAKoC,SAAS,EAAC,gFAAgF;UAAAmC,QAAA,gBAC7FvE,OAAA;YAAKoC,SAAS,EAAC,uCAAuC;YAAAmC,QAAA,EACnDL,iBAAiB,CAACpB;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACNxC,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAmC,QAAA,EAC/CL,iBAAiB,CAACpB,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG;UAAW;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxC,OAAA;UAAKoC,SAAS,EAAC,gFAAgF;UAAAmC,QAAA,gBAC7FvE,OAAA;YAAKoC,SAAS,EAAC,yCAAyC;YAAAmC,QAAA,EACrDL,iBAAiB,CAACC,MAAM,CAACK,CAAC,IAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNxC,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAmC,QAAA,EAC/CL,iBAAiB,CAACC,MAAM,CAACK,CAAC,IAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,KAAK,CAAC,GAAG,gBAAgB,GAAG;UAAe;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxC,OAAA;UAAKoC,SAAS,EAAC,gFAAgF;UAAAmC,QAAA,gBAC7FvE,OAAA;YAAKoC,SAAS,EAAC,wCAAwC;YAAAmC,QAAA,GACpD5D,IAAI,CAACmD,KAAK,CACTI,iBAAiB,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEe,CAAC,KAAKf,GAAG,GAAGe,CAAC,CAACC,QAAQ,EAAE,CAAC,CAAC,GAAG,EAC9D,CAAC,EAAC,KACJ;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxC,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAmC,QAAA,EAAC;UAAa;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAENxC,OAAA;UAAKoC,SAAS,EAAC,gFAAgF;UAAAmC,QAAA,gBAC7FvE,OAAA;YAAKoC,SAAS,EAAC,yCAAyC;YAAAmC,QAAA,EACrDL,iBAAiB,CACfC,MAAM,CAACK,CAAC,IAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAC7BhB,MAAM,CAAC,CAACC,GAAG,EAAEe,CAAC,KAAKf,GAAG,GAAGN,kBAAkB,CAACqB,CAAC,CAAC,CAAClB,KAAK,EAAE,CAAC,CAAC,CACxDoB,cAAc,CAAC;UAAC;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CAAC,eACNxC,OAAA;YAAKoC,SAAS,EAAC,mCAAmC;YAAAmC,QAAA,EAC/CL,iBAAiB,CACfC,MAAM,CAACK,CAAC,IAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAC7BhB,MAAM,CAAC,CAACC,GAAG,EAAEe,CAAC,KAAKf,GAAG,GAAGN,kBAAkB,CAACqB,CAAC,CAAC,CAAClB,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG;UAAS;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxC,OAAA;QAAKoC,SAAS,EAAC,oCAAoC;QAAAmC,QAAA,gBACjDvE,OAAA;UAAKoC,SAAS,EAAC,2CAA2C;UAAAmC,QAAA,gBACxDvE,OAAA;YAAMoC,SAAS,EAAC,eAAe;YAAAmC,QAAA,EAAC;UAAyB;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChExC,OAAA;YAAMoC,SAAS,EAAC,2BAA2B;YAAAmC,QAAA,EACxCL,iBAAiB,CAACpB,MAAM,GAAG,CAAC,GACzB,GAAGnC,IAAI,CAACmD,KAAK,CAAEI,iBAAiB,CAACC,MAAM,CAACK,CAAC,IAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,GAAGoB,iBAAiB,CAACpB,MAAM,GAAI,GAAG,CAAC,GAAG,GAC3G;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNxC,OAAA;UAAKoC,SAAS,EAAC,0CAA0C;UAAAmC,QAAA,eACvDvE,OAAA;YACEoC,SAAS,EAAC,2FAA2F;YACrGuC,KAAK,EAAE;cACLC,KAAK,EAAEV,iBAAiB,CAACpB,MAAM,GAAG,CAAC,GAC/B,GAAIoB,iBAAiB,CAACC,MAAM,CAACK,CAAC,IAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,GAAGoB,iBAAiB,CAACpB,MAAM,GAAI,GAAG,GAAG,GAC/F;YACN;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDxC,OAAA;MAAKoC,SAAS,EAAC,WAAW;MAAAmC,QAAA,gBACxBvE,OAAA;QAAKoC,SAAS,EAAC,mCAAmC;QAAAmC,QAAA,gBAChDvE,OAAA;UAAIoC,SAAS,EAAC,qCAAqC;UAAAmC,QAAA,GAAC,wBAElD,EAACL,iBAAiB,CAACpB,MAAM,GAAG,CAAC,iBAC3B9C,OAAA;YAAMoC,SAAS,EAAC,wCAAwC;YAAAmC,QAAA,GAAC,GACtD,EAACL,iBAAiB,CAACpB,MAAM,EAAC,GAC7B;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACLxC,OAAA;UAAKoC,SAAS,EAAC,uBAAuB;UAAAmC,QAAA,EAAC;QAEvC;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL0B,iBAAiB,CAACnB,GAAG,CAAEf,OAAO,IAAK;QAClC,MAAM6C,KAAK,GAAG1B,kBAAkB,CAACnB,OAAO,CAAC;QACzC,MAAM8C,UAAU,GAAGxE,eAAe,KAAK0B,OAAO,CAAC+C,EAAE;QAEjD,oBACW/E,OAAA;UAERoC,SAAS,EAAE,0FACT0C,UAAU,GACN,+DAA+D,GAC/D,uFAAuF,EAC1F;UAAAP,QAAA,gBAGSvE,OAAA;YACVoC,SAAS,EAAE,wCACT0C,UAAU,GACN,iGAAiG,GACjG,mFAAmF,EACtF;YACHE,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC/B,OAAO,CAAC+C,EAAE,CAAE;YAAAR,QAAA,eAE3CvE,OAAA;cAAKoC,SAAS,EAAC,mCAAmC;cAAAmC,QAAA,gBAChDvE,OAAA;gBAAKoC,SAAS,EAAC,4CAA4C;gBAAAmC,QAAA,gBAEzDvE,OAAA;kBAAQoC,SAAS,EAAC,uEAAuE;kBAAAmC,QAAA,EACtFO,UAAU,gBACT9E,OAAA,CAACb,WAAW;oBAACiD,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEnCxC,OAAA,CAACZ,YAAY;oBAACgD,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACpC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eAGTxC,OAAA;kBAAKoC,SAAS,EAAC,gBAAgB;kBAAAmC,QAAA,gBAC7BvE,OAAA;oBAAKoC,SAAS,EAAC,kCAAkC;oBAAAmC,QAAA,gBAC/CvE,OAAA;sBAAIoC,SAAS,EAAC,8CAA8C;sBAAAmC,QAAA,EACzDvC,OAAO,CAACiD;oBAAK;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACLxC,OAAA;sBAAKoC,SAAS,EAAC,6BAA6B;sBAAAmC,QAAA,GACzCxC,aAAa,CAACC,OAAO,CAAC,eACvBhC,OAAA;wBAAMoC,SAAS,EAAC,uBAAuB;wBAAAmC,QAAA,EACpC9B,aAAa,CAACT,OAAO;sBAAC;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEexC,OAAA;oBAAKoC,SAAS,EAAC,6DAA6D;oBAAAmC,QAAA,gBAC/FvE,OAAA;sBAAKoC,SAAS,EAAC,6BAA6B;sBAAAmC,QAAA,gBAC1CvE,OAAA,CAACX,QAAQ;wBAAC+C,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChCxC,OAAA;wBAAAuE,QAAA,EAAOtD,UAAU,CAACe,OAAO,CAACd,IAAI;sBAAC;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC,eAENxC,OAAA;sBAAKoC,SAAS,EAAC,6BAA6B;sBAAAmC,QAAA,gBAC1CvE,OAAA,CAACV,KAAK;wBAAC8C,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7BxC,OAAA;wBAAAuE,QAAA,EAAO/D,cAAc,CAACwB,OAAO,CAACyC,QAAQ;sBAAC;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eAENxC,OAAA;sBAAKoC,SAAS,EAAC,6BAA6B;sBAAAmC,QAAA,gBAC1CvE,OAAA,CAACT,KAAK;wBAAC6C,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7BxC,OAAA;wBAAAuE,QAAA,EAAO3B,kBAAkB,CAACZ,OAAO;sBAAC;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,EAELyB,aAAa,CAACjC,OAAO,CAAC,iBACrBhC,OAAA;sBAAKoC,SAAS,EAAC,6BAA6B;sBAAAmC,QAAA,gBAC1CvE,OAAA,CAACR,QAAQ;wBAAC4C,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChCxC,OAAA;wBAAAuE,QAAA,GACGM,KAAK,CAACvB,KAAK,CAACoB,cAAc,CAAC,CAAC,EAAC,sBAC7B,EAACG,KAAK,CAACtB,UAAU,EAAC,cACrB;sBAAA;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNxC,OAAA;gBAAKoC,SAAS,EAAC,2CAA2C;gBAAAmC,QAAA,GACvDvC,OAAO,CAACW,SAAS,iBAChB3C,OAAA;kBACEgF,OAAO,EAAGE,CAAC,IAAK;oBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;oBACnB;kBACF,CAAE;kBACF/C,SAAS,EAAC,gGAAgG;kBAC1G6C,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,eAEtBvE,OAAA,CAACH,OAAO;oBAACuC,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CACT,eAEDxC,OAAA;kBACEgF,OAAO,EAAGE,CAAC,IAAK;oBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;oBACnB/E,eAAe,CAAC4B,OAAO,CAAC;kBAC1B,CAAE;kBACFI,SAAS,EAAC,gGAAgG;kBAC1G6C,KAAK,EAAC,aAAa;kBAAAV,QAAA,eAEnBvE,OAAA,CAACJ,QAAQ;oBAACwC,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGJsC,UAAU,iBACT9E,OAAA;YAAKoC,SAAS,EAAC,gBAAgB;YAC1BuC,KAAK,EAAE;cACLS,SAAS,EAAE;YACb,CAAE;YAAAb,QAAA,EAELN,aAAa,CAACjC,OAAO,CAAC,gBACrBhC,OAAA,CAACF,4BAA4B;cAC3BI,QAAQ,EAAE,CAAC8B,OAAO,CAAE;cACpBqD,eAAe,EAAEA,CAAA,KAAM,CAAC;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,gBAEFxC,OAAA;cAAKoC,SAAS,EAAC,iBAAiB;cAAAmC,QAAA,gBAC9BvE,OAAA;gBAAKoC,SAAS,EAAC,kFAAkF;gBAAAmC,QAAA,EAC9FvC,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,YAAY,gBACjDnC,OAAA,CAACL,OAAO;kBAACyC,SAAS,EAAC;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GACxDR,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,QAAQ,gBAChDnC,OAAA,CAACN,WAAW;kBAAC0C,SAAS,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEhDxC,OAAA,CAACR,QAAQ;kBAAC4C,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAC9C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENxC,OAAA;gBAAIoC,SAAS,EAAC,wCAAwC;gBAAAmC,QAAA,EACnDvC,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,YAAY,GAC/C,4BAA4B,GAC5BH,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,QAAQ,GAC9C,0BAA0B,GAC1B;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEvB,CAAC,eAELxC,OAAA;gBAAGoC,SAAS,EAAC,uBAAuB;gBAAAmC,QAAA,EACjCvC,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,YAAY,GAC/C,uBAAuBH,OAAO,CAACE,mBAAmB,CAACQ,QAAQ,IAAI,CAAC,GAAG,GACnEV,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,QAAQ,GAC9CH,OAAO,CAACE,mBAAmB,CAACoD,KAAK,IAAI,iBAAiB,GACtDtD,OAAO,CAACW,SAAS,GACjB,iDAAiD,GACjD;cAAiC;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEpC,CAAC,EAEHR,OAAO,CAACE,mBAAmB,CAACC,KAAK,KAAK,YAAY,IAClDH,OAAO,CAACE,mBAAmB,CAACQ,QAAQ,iBACnC1C,OAAA;gBAAKoC,SAAS,EAAC,gDAAgD;gBAAAmC,QAAA,eAC7DvE,OAAA;kBACEoC,SAAS,EAAC,0DAA0D;kBACpEuC,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG5C,OAAO,CAACE,mBAAmB,CAACQ,QAAQ;kBAAI;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA,GA3JKR,OAAO,CAAC+C,EAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4Jb,CAAC;MAER,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA1aWJ,0BAAqE;AAAAsF,EAAA,GAArEtF,0BAAqE;AAAA,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}