{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { MeetingsList, ErrorBoundary, WhisperStatusIndicator, TranscriptionManager, RecordingPanel, CollapsibleTranscriptsList } from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { createDemoMeetings } from './utils/demoData';\nimport { Headphones, Plus, Mic2, TestTube, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [meetings, setMeetings] = useState([]);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState(null);\n  const [activeView, setActiveView] = useState('recording');\n\n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n  const {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  } = useAudioRecorder();\n  const {\n    transcript,\n    isTranscribing,\n    transcribeAudioEnhanced,\n    cancelTranscription,\n    editSegment,\n    clearTranscript,\n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n  const handleStartRecording = useCallback(async title => {\n    try {\n      await startRecording();\n      const newMeeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started'\n        }\n      };\n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      if (currentMeeting && audioBlob) {\n        const updatedMeeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started'\n          }\n        };\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m));\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n  const handleStartTranscription = useCallback(async meetingId => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date()\n      }\n    };\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: progress => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: {\n              ...m.transcriptionStatus,\n              progress,\n              state: 'processing'\n            }\n          } : m));\n        },\n        onStatusUpdate: status => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: status\n          } : m));\n        },\n        enhanceSpeakers: true\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date()\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      setActiveView('transcript');\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence\n      });\n    } catch (error) {\n      console.error('❌ Transkribavimo klaida:', error);\n      const errorMeeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n  const handleCancelTranscription = useCallback(meetingId => {\n    cancelTranscription();\n    setMeetings(prev => prev.map(m => m.id === meetingId ? {\n      ...m,\n      transcriptionStatus: {\n        ...m.transcriptionStatus,\n        state: 'cancelled'\n      }\n    } : m));\n  }, [cancelTranscription]);\n  const handleEditSegment = useCallback((meetingId, segmentId, newText) => {\n    editSegment(segmentId, newText);\n\n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => {\n      var _meeting$transcript;\n      return meeting.id === meetingId ? {\n        ...meeting,\n        transcript: (_meeting$transcript = meeting.transcript) === null || _meeting$transcript === void 0 ? void 0 : _meeting$transcript.map(segment => segment.id === segmentId ? {\n          ...segment,\n          text: newText,\n          isEdited: true,\n          editedAt: new Date(),\n          editedBy: 'user'\n        } : segment)\n      } : meeting;\n    }));\n  }, [editSegment]);\n  const handleSelectMeeting = useCallback(meeting => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n  const handleDeleteMeeting = useCallback(meetingId => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if ((currentMeeting === null || currentMeeting === void 0 ? void 0 : currentMeeting.id) === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if ((selectedMeetingForTranscript === null || selectedMeetingForTranscript === void 0 ? void 0 : selectedMeetingForTranscript.id) === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n  const handleExportMeeting = useCallback(meeting => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed',\n        progress: 100,\n        completedAt: meeting.date\n      }\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `min-h-screen bg-app-gradient font-inter relative overflow-hidden ${activeView === 'recording' ? 'grid-page-recording' : activeView === 'transcription' ? 'grid-page-transcription' : activeView === 'transcript' ? 'grid-page-results' : 'grid-page-home'}`,\n      style: {\n        '--grid-size': `${gridSize}px`,\n        '--grid-rotation': `${gridRotation}deg`,\n        '--grid-color': gridColor\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gradient-to-tr from-purple-900/06 via-transparent via-purple-800/04 to-slate-900/08 pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gradient-to-bl from-transparent via-purple-700/03 via-slate-800/02 to-purple-900/04 pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-20 left-20 w-96 h-96 bg-gradient-radial from-purple-600/08 to-transparent rounded-full pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-20 right-20 w-80 h-80 bg-gradient-radial from-purple-500/06 to-transparent rounded-full pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"enhanced-glass border-b border-white/30 shadow-elegant sticky top-0 z-50 transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 lg:px-8\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between h-14\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"inline-flex items-center space-x-3 px-3 py-1.5 rounded-full bg-gradient-to-r from-slate-50/90 via-blue-50/80 to-indigo-50/90 backdrop-blur-sm border border-white/40 shadow-soft transition-all duration-200 hover:shadow-elegant hover:from-slate-100/90 hover:via-blue-100/80 hover:to-indigo-100/90\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-6 h-6 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(Mic2, {\n                      className: \"h-3.5 w-3.5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-sm font-semibold bg-gradient-to-r from-gray-800 to-gray-700 bg-clip-text text-transparent\",\n                    children: \"MOM Recorder\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1 bg-gradient-to-r from-white/75 via-slate-50/70 to-white/75 backdrop-blur-sm border border-white/40 p-1 rounded-full shadow-soft\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('recording'),\n                    className: `inline-flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${activeView === 'recording' ? 'bg-gradient-to-r from-white/95 via-blue-50/80 to-white/95 text-gray-900 shadow-soft backdrop-blur-sm border border-white/60' : 'text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-white/60 hover:via-slate-50/50 hover:to-white/60'}`,\n                    children: [/*#__PURE__*/_jsxDEV(Mic2, {\n                      className: \"h-3.5 w-3.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u012Era\\u0161ymas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcription'),\n                    className: `inline-flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${activeView === 'transcription' ? 'bg-gradient-to-r from-white/95 via-purple-50/80 to-white/95 text-gray-900 shadow-soft backdrop-blur-sm border border-white/60' : 'text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-white/60 hover:via-slate-50/50 hover:to-white/60'}`,\n                    children: [/*#__PURE__*/_jsxDEV(Zap, {\n                      className: \"h-3.5 w-3.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Transkribavimas\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setActiveView('transcript'),\n                    className: `inline-flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${activeView === 'transcript' ? 'bg-gradient-to-r from-white/95 via-green-50/80 to-white/95 text-gray-900 shadow-soft backdrop-blur-sm border border-white/60' : 'text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-white/60 hover:via-slate-50/50 hover:to-white/60'}`,\n                    children: [/*#__PURE__*/_jsxDEV(Headphones, {\n                      className: \"h-3.5 w-3.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Rezultatai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 17\n                }, this), meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: loadDemoData,\n                  className: \"inline-flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-800 bg-gradient-to-r from-amber-50/70 via-orange-50/60 to-amber-50/70 hover:from-amber-100/80 hover:via-orange-100/70 hover:to-amber-100/80 backdrop-blur-sm border border-white/40 hover:border-amber-200/50 rounded-full shadow-soft hover:shadow-elegant transition-all duration-200\",\n                  title: \"U\\u017Ekrauti demonstracinius duomenis\",\n                  children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                    className: \"h-3.5 w-3.5 text-amber-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Demo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setActiveView('recording'),\n                  className: \"inline-flex items-center space-x-2 px-4 py-1.5 text-sm font-semibold text-white bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 rounded-full shadow-primary hover:shadow-gradient transition-all duration-200 backdrop-blur-sm border border-blue-400/30 hover:border-blue-300/40\",\n                  children: [/*#__PURE__*/_jsxDEV(Plus, {\n                    className: \"h-3.5 w-3.5 drop-shadow-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"drop-shadow-sm\",\n                    children: \"Naujas pokalbis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"max-w-7xl mx-auto py-6 px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-120px)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-2\",\n              children: [activeView === 'recording' && /*#__PURE__*/_jsxDEV(RecordingPanel, {\n                recordingState: recordingState,\n                currentMeeting: currentMeeting,\n                onStartRecording: handleStartRecording,\n                onStopRecording: handleStopRecording,\n                onPauseRecording: pauseRecording,\n                onResumeRecording: resumeRecording\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), activeView === 'transcription' && /*#__PURE__*/_jsxDEV(TranscriptionManager, {\n                meetings: meetings,\n                onStartTranscription: handleStartTranscription,\n                onCancelTranscription: handleCancelTranscription,\n                isTranscribing: isTranscribing,\n                currentTranscriptionId: currentTranscriptionId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this), activeView === 'transcript' && /*#__PURE__*/_jsxDEV(CollapsibleTranscriptsList, {\n                meetings: meetings,\n                onEditSegment: handleEditSegment,\n                onExportMeeting: handleExportMeeting\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(MeetingsList, {\n                meetings: meetings,\n                currentMeeting: currentMeeting,\n                onSelectMeeting: handleSelectMeeting,\n                onDeleteMeeting: handleDeleteMeeting,\n                onExportMeeting: handleExportMeeting,\n                isRecording: recordingState.isRecording\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"QZGo74Iv2G+R2QYYb+sHjp3F0Ks=\", false, function () {\n  return [useAudioRecorder, useTranscription];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "RecordingPanel", "CollapsibleTranscriptsList", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "jsxDEV", "_jsxDEV", "App", "_s", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "gridSize", "setGridSize", "gridRotation", "setGridRotation", "gridColor", "setGridColor", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "children", "className", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "onStartTranscription", "onCancelTranscription", "onEditSegment", "onExportMeeting", "onSelectMeeting", "onDeleteMeeting", "isRecording", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { \n  Recording<PERSON>utton, \n  RecordingIndicator, \n  TranscriptViewer, \n  MeetingsList, \n  ErrorBoundary, \n  WhisperConfig,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  RecordingPanel,\n  CollapsibleTranscriptsList,\n  GridControls\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings } from 'lucide-react';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  \n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  return (\n    <ErrorBoundary>\n      <div \n        className={`min-h-screen bg-app-gradient font-inter relative overflow-hidden ${\n          activeView === 'recording' ? 'grid-page-recording' :\n          activeView === 'transcription' ? 'grid-page-transcription' :\n          activeView === 'transcript' ? 'grid-page-results' :\n          'grid-page-home'\n        }`}\n        style={{\n          '--grid-size': `${gridSize}px`,\n          '--grid-rotation': `${gridRotation}deg`,\n          '--grid-color': gridColor,\n        } as React.CSSProperties}\n      >\n        {/* Static purple overlay layers - no distracting animations */}\n        <div className=\"fixed inset-0 bg-gradient-to-tr from-purple-900/06 via-transparent via-purple-800/04 to-slate-900/08 pointer-events-none\" />\n        <div className=\"fixed inset-0 bg-gradient-to-bl from-transparent via-purple-700/03 via-slate-800/02 to-purple-900/04 pointer-events-none\" />\n        {/* Subtle static purple accent spots */}\n        <div className=\"fixed top-20 left-20 w-96 h-96 bg-gradient-radial from-purple-600/08 to-transparent rounded-full pointer-events-none\" />\n        <div className=\"fixed bottom-20 right-20 w-80 h-80 bg-gradient-radial from-purple-500/06 to-transparent rounded-full pointer-events-none\" />\n        {/* Content wrapper */}\n        <div className=\"relative z-10\">\n        {/* Enhanced Header */}\n        <header className=\"enhanced-glass border-b border-white/30 shadow-elegant sticky top-0 z-50 transition-all duration-300\">\n          <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between h-14\">\n              <div className=\"flex items-center space-x-4\">\n                {/* Elegant Gradient Logo */}\n                <div className=\"inline-flex items-center space-x-3 px-3 py-1.5 rounded-full bg-gradient-to-r from-slate-50/90 via-blue-50/80 to-indigo-50/90 backdrop-blur-sm border border-white/40 shadow-soft transition-all duration-200 hover:shadow-elegant hover:from-slate-100/90 hover:via-blue-100/80 hover:to-indigo-100/90\">\n                  <div className=\"w-6 h-6 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm\">\n                    <Mic2 className=\"h-3.5 w-3.5 text-white\" />\n                  </div>\n                  <h1 className=\"text-sm font-semibold bg-gradient-to-r from-gray-800 to-gray-700 bg-clip-text text-transparent\">MOM Recorder</h1>\n              </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                {/* Whisper Status Indicator */}\n                <WhisperStatusIndicator />\n            \n                {/* Elegant Gradient View Switcher */}\n                <div className=\"flex items-center space-x-1 bg-gradient-to-r from-white/75 via-slate-50/70 to-white/75 backdrop-blur-sm border border-white/40 p-1 rounded-full shadow-soft\">\n            <button\n                    onClick={() => setActiveView('recording')}\n                    className={`inline-flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${\n                      activeView === 'recording'\n                        ? 'bg-gradient-to-r from-white/95 via-blue-50/80 to-white/95 text-gray-900 shadow-soft backdrop-blur-sm border border-white/60'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-white/60 hover:via-slate-50/50 hover:to-white/60'\n                    }`}\n                  >\n                    <Mic2 className=\"h-3.5 w-3.5\" />\n                    <span>Įrašymas</span>\n            </button>\n                  <button\n                    onClick={() => setActiveView('transcription')}\n                    className={`inline-flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${\n                      activeView === 'transcription'\n                        ? 'bg-gradient-to-r from-white/95 via-purple-50/80 to-white/95 text-gray-900 shadow-soft backdrop-blur-sm border border-white/60'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-white/60 hover:via-slate-50/50 hover:to-white/60'\n                    }`}\n                  >\n                    <Zap className=\"h-3.5 w-3.5\" />\n                    <span>Transkribavimas</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveView('transcript')}\n                    className={`inline-flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium rounded-full transition-all duration-200 ${\n                      activeView === 'transcript'\n                        ? 'bg-gradient-to-r from-white/95 via-green-50/80 to-white/95 text-gray-900 shadow-soft backdrop-blur-sm border border-white/60'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-white/60 hover:via-slate-50/50 hover:to-white/60'\n                    }`}\n                  >\n                    <Headphones className=\"h-3.5 w-3.5\" />\n                    <span>Rezultatai</span>\n                  </button>\n                </div>\n\n                {meetings.length === 0 && (\n                  <button\n                    onClick={loadDemoData}\n                    className=\"inline-flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-800 bg-gradient-to-r from-amber-50/70 via-orange-50/60 to-amber-50/70 hover:from-amber-100/80 hover:via-orange-100/70 hover:to-amber-100/80 backdrop-blur-sm border border-white/40 hover:border-amber-200/50 rounded-full shadow-soft hover:shadow-elegant transition-all duration-200\"\n                    title=\"Užkrauti demonstracinius duomenis\"\n                  >\n                    <TestTube className=\"h-3.5 w-3.5 text-amber-600\" />\n                    <span>Demo</span>\n                  </button>\n                )}\n                {/* Elegant Primary Action Button */}\n                <button\n                  onClick={() => setActiveView('recording')}\n                  className=\"inline-flex items-center space-x-2 px-4 py-1.5 text-sm font-semibold text-white bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 rounded-full shadow-primary hover:shadow-gradient transition-all duration-200 backdrop-blur-sm border border-blue-400/30 hover:border-blue-300/40\"\n                >\n                  <Plus className=\"h-3.5 w-3.5 drop-shadow-sm\" />\n                  <span className=\"drop-shadow-sm\">Naujas pokalbis</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n\n\n        {/* Main Content */}\n        <main className=\"max-w-7xl mx-auto py-6 px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-120px)]\">\n            \n            {/* Left Column - Recording/Transcription Management */}\n            <div className=\"lg:col-span-2\">\n              {activeView === 'recording' && (\n                <RecordingPanel\n                    recordingState={recordingState}\n                  currentMeeting={currentMeeting}\n                  onStartRecording={handleStartRecording}\n                  onStopRecording={handleStopRecording}\n                  onPauseRecording={pauseRecording}\n                  onResumeRecording={resumeRecording}\n                />\n              )}\n\n              {activeView === 'transcription' && (\n                <TranscriptionManager\n                  meetings={meetings}\n                  onStartTranscription={handleStartTranscription}\n                  onCancelTranscription={handleCancelTranscription}\n                  isTranscribing={isTranscribing}\n                  currentTranscriptionId={currentTranscriptionId}\n                />\n              )}\n\n              {activeView === 'transcript' && (\n                <CollapsibleTranscriptsList\n                  meetings={meetings}\n                  onEditSegment={handleEditSegment}\n                  onExportMeeting={handleExportMeeting}\n                />\n              )}\n          </div>\n\n            {/* Right Column - Meetings List */}\n            <div className=\"lg:col-span-1\">\n            <MeetingsList\n              meetings={meetings}\n                currentMeeting={currentMeeting}\n              onSelectMeeting={handleSelectMeeting}\n              onDeleteMeeting={handleDeleteMeeting}\n              onExportMeeting={handleExportMeeting}\n                isRecording={recordingState.isRecording}\n            />\n          </div>\n        </div>\n      </main>\n    </div>\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAIEC,YAAY,EACZC,aAAa,EAEbC,sBAAsB,EACtBC,oBAAoB,EAEpBC,cAAc,EACdC,0BAA0B,QAErB,cAAc;AACrB,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAE5D,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,QAAkB,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACwB,4BAA4B,EAAEC,+BAA+B,CAAC,GAAGzB,QAAQ,CAAiB,IAAI,CAAC;EACtG,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAA+C,WAAW,CAAC;;EAEvG;EACA,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,GAAG,CAAC;EAC7C,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,0BAA0B,CAAC;EAEtE,MAAM;IAAEkC,cAAc;IAAEC,cAAc;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAgB,CAAC,GAAG9B,gBAAgB,CAAC,CAAC;EAC7G,MAAM;IACJ+B,UAAU;IACVC,cAAc;IACdC,uBAAuB;IACvBC,mBAAmB;IACnBC,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC,sBAAsB;IACtBC,QAAQ;IACRC;EACF,CAAC,GAAGvC,gBAAgB,CAAC,CAAC;EAEtB,MAAMwC,oBAAoB,GAAGhD,WAAW,CAAC,MAAOiD,KAAa,IAAK;IAChE,IAAI;MACF,MAAMf,cAAc,CAAC,CAAC;MAEtB,MAAMgB,UAAmB,GAAG;QAC1BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBL,KAAK,EAAEA,KAAK;QACZM,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC;QAChBI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE;UACnBC,KAAK,EAAE;QACT;MACF,CAAC;MAEDrC,iBAAiB,CAAC4B,UAAU,CAAC;MAC7B9B,WAAW,CAACwC,IAAI,IAAI,CAACV,UAAU,EAAE,GAAGU,IAAI,CAAC,CAAC;MAC1ClC,aAAa,CAAC,WAAW,CAAC;MAC1BiB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAAC3B,cAAc,EAAES,eAAe,CAAC,CAAC;EAErC,MAAMoB,mBAAmB,GAAG/D,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMgE,SAAS,GAAG,MAAM7B,aAAa,CAAC,CAAC;MAEvC,IAAId,cAAc,IAAI2C,SAAS,EAAE;QAC/B,MAAMC,cAAuB,GAAG;UAC9B,GAAG5C,cAAc;UACjBoC,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAEU,IAAI,CAACC,KAAK,CAAC,CAACf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhC,cAAc,CAACkC,IAAI,CAACa,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACzEJ,SAAS;UACTN,mBAAmB,EAAE;YACnBC,KAAK,EAAE;UACT;QACF,CAAC;QAEDrC,iBAAiB,CAAC2C,cAAc,CAAC;QACjC7C,WAAW,CAACwC,IAAI,IACdA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAK9B,cAAc,CAAC8B,EAAE,GAAGc,cAAc,GAAGK,CAAC,CAC/D,CAAC;;QAED;QACA5C,aAAa,CAAC,eAAe,CAAC;MAChC;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDU,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC,EAAE,CAACpC,aAAa,EAAEd,cAAc,CAAC,CAAC;EAEnC,MAAMmD,wBAAwB,GAAGxE,WAAW,CAAC,MAAOyE,SAAiB,IAAK;IACxE,MAAMC,OAAO,GAAGvD,QAAQ,CAACwD,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACV,SAAS,EAAE;;IAEpC;IACA,MAAMC,cAAuB,GAAG;MAC9B,GAAGS,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,SAAS;QAChBiB,SAAS,EAAE,IAAIxB,IAAI,CAAC;MACtB;IACF,CAAC;IAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGR,cAAc,GAAGK,CAAC,CAAC,CAAC;IAC3E9C,+BAA+B,CAACyC,cAAc,CAAC;IAE/C,IAAI;MACF;MACA,MAAMY,MAAM,GAAG,MAAMrC,uBAAuB,CAACkC,OAAO,CAACV,SAAS,EAAES,SAAS,EAAE;QACzEK,UAAU,EAAGhC,QAAQ,IAAK;UACxB1B,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAE;cACnB,GAAGY,CAAC,CAACZ,mBAAmB;cACxBZ,QAAQ;cACRa,KAAK,EAAE;YACT;UACF,CAAC,GACDW,CACN,CAAC,CAAC;QACJ,CAAC;QACDS,cAAc,EAAGtB,MAAM,IAAK;UAC1BrC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAED;UACvB,CAAC,GACDa,CACN,CAAC,CAAC;QACJ,CAAC;QACDU,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAyB,GAAG;QAChC,GAAGhB,cAAc;QACjB3B,UAAU,EAAEuC,MAAM,CAACK,QAAQ;QAC3BC,YAAY,EAAEN,MAAM,CAACO,QAAQ;QAC7BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzB3B,mBAAmB,EAAE;UACnBC,KAAK,EAAE,WAAW;UAClBb,QAAQ,EAAE,GAAG;UACb8B,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB,SAAS;UACvDU,WAAW,EAAE,IAAIlC,IAAI,CAAC;QACxB;MACF,CAAC;MAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGQ,gBAAgB,GAAGX,CAAC,CAAC,CAAC;MAC7E9C,+BAA+B,CAACyD,gBAAgB,CAAC;MACjDvD,aAAa,CAAC,YAAY,CAAC;MAE3BoC,OAAO,CAACyB,GAAG,CAAC,sCAAsC,EAAE;QAClDL,QAAQ,EAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM;QAChCJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM;QAChCC,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU;QACjCC,UAAU,EAAEd,MAAM,CAACQ,QAAQ,CAACO;MAC9B,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhD,MAAMgC,YAAqB,GAAG;QAC5B,GAAG5B,cAAc;QACjBP,mBAAmB,EAAE;UACnBC,KAAK,EAAE,QAAQ;UACfE,KAAK,EAAEA,KAAK,CAACiC,OAAO;UACpBlB,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB;QAChD;MACF,CAAC;MAEDxD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGoB,YAAY,GAAGvB,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACnD,QAAQ,EAAEqB,uBAAuB,CAAC,CAAC;EAEvC,MAAMuD,yBAAyB,GAAG/F,WAAW,CAAEyE,SAAiB,IAAK;IACnEhC,mBAAmB,CAAC,CAAC;IAErBrB,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;MACE,GAAGH,CAAC;MACJZ,mBAAmB,EAAE;QACnB,GAAGY,CAAC,CAACZ,mBAAmB;QACxBC,KAAK,EAAE;MACT;IACF,CAAC,GACDW,CACN,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,mBAAmB,CAAC,CAAC;EAEzB,MAAMuD,iBAAiB,GAAGhG,WAAW,CAAC,CAACyE,SAAiB,EAAEwB,SAAiB,EAAEC,OAAe,KAAK;IAC/FxD,WAAW,CAACuD,SAAS,EAAEC,OAAO,CAAC;;IAE/B;IACA9E,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACK,OAAO;MAAA,IAAAyB,mBAAA;MAAA,OAClCzB,OAAO,CAACvB,EAAE,KAAKsB,SAAS,GACpB;QACE,GAAGC,OAAO;QACVpC,UAAU,GAAA6D,mBAAA,GAAEzB,OAAO,CAACpC,UAAU,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,IACzCA,OAAO,CAACjD,EAAE,KAAK8C,SAAS,GACpB;UACE,GAAGG,OAAO;UACVC,IAAI,EAAEH,OAAO;UACbI,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,IAAInD,IAAI,CAAC,CAAC;UACpBoD,QAAQ,EAAE;QACZ,CAAC,GACDJ,OACN;MACF,CAAC,GACD1B,OAAO;IAAA,CACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;EAEjB,MAAM+D,mBAAmB,GAAGzG,WAAW,CAAE0E,OAAgB,IAAK;IAC5DpD,iBAAiB,CAACoD,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACpC,UAAU,IAAIoC,OAAO,CAACpC,UAAU,CAACkD,MAAM,GAAG,CAAC,EAAE;MACvDhE,+BAA+B,CAACkD,OAAO,CAAC;MACxChD,aAAa,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgF,mBAAmB,GAAG1G,WAAW,CAAEyE,SAAiB,IAAK;IAC7DrD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAAC+C,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC,CAAC;IACzD,IAAI,CAAApD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,EAAE,MAAKsB,SAAS,EAAE;MACpCnD,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAAC,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAE4B,EAAE,MAAKsB,SAAS,EAAE;MAClDjD,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,cAAc,EAAEE,4BAA4B,CAAC,CAAC;EAElD,MAAMqF,mBAAmB,GAAG5G,WAAW,CAAE0E,OAAgB,IAAK;IAC5D,MAAMmC,UAAU,GAAG;MACjB5D,KAAK,EAAEyB,OAAO,CAACzB,KAAK;MACpBM,IAAI,EAAEmB,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC;MAChCtD,QAAQ,EAAEkB,OAAO,CAAClB,QAAQ;MAC1BlB,UAAU,EAAEoC,OAAO,CAACpC,UAAU,IAAIA,UAAU;MAC5C6C,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,EAAE;MACxCE,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC;MAChC3B,mBAAmB,EAAEgB,OAAO,CAAChB;IAC/B,CAAC;IAED,MAAMqD,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMK,OAAO,GAAG,sCAAsC,GAAEC,kBAAkB,CAACJ,OAAO,CAAC;IAEnF,MAAMK,qBAAqB,GAAG,WAAW1C,OAAO,CAACzB,KAAK,CAACoE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAE5I,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,EAAET,OAAO,CAAC;IACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,EAAEP,qBAAqB,CAAC;IAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC;EACrB,CAAC,EAAE,CAACtF,UAAU,CAAC,CAAC;EAIhB,MAAMuF,YAAY,GAAG7H,WAAW,CAAC,MAAM;IACrC,MAAM8H,YAAY,GAAGrH,kBAAkB,CAAC,CAAC,CAAC4D,GAAG,CAACK,OAAO,KAAK;MACxD,GAAGA,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,WAAoB;QAC3Bb,QAAQ,EAAE,GAAG;QACbwC,WAAW,EAAEZ,OAAO,CAACnB;MACvB;IACF,CAAC,CAAC,CAAC;IACHnC,WAAW,CAAC0G,YAAY,CAAC;IACzBpG,aAAa,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEV,OAAA,CAACd,aAAa;IAAA6H,QAAA,eACZ/G,OAAA;MACEgH,SAAS,EAAE,oEACTvG,UAAU,KAAK,WAAW,GAAG,qBAAqB,GAClDA,UAAU,KAAK,eAAe,GAAG,yBAAyB,GAC1DA,UAAU,KAAK,YAAY,GAAG,mBAAmB,GACjD,gBAAgB,EACf;MACHwG,KAAK,EAAE;QACL,aAAa,EAAE,GAAGtG,QAAQ,IAAI;QAC9B,iBAAiB,EAAE,GAAGE,YAAY,KAAK;QACvC,cAAc,EAAEE;MAClB,CAAyB;MAAAgG,QAAA,gBAGzB/G,OAAA;QAAKgH,SAAS,EAAC;MAA0H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5IrH,OAAA;QAAKgH,SAAS,EAAC;MAA0H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE5IrH,OAAA;QAAKgH,SAAS,EAAC;MAAsH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxIrH,OAAA;QAAKgH,SAAS,EAAC;MAA0H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE5IrH,OAAA;QAAKgH,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE9B/G,OAAA;UAAQgH,SAAS,EAAC,sGAAsG;UAAAD,QAAA,eACtH/G,OAAA;YAAKgH,SAAS,EAAC,gCAAgC;YAAAD,QAAA,eAC7C/G,OAAA;cAAKgH,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACrD/G,OAAA;gBAAKgH,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,eAE1C/G,OAAA;kBAAKgH,SAAS,EAAC,wSAAwS;kBAAAD,QAAA,gBACrT/G,OAAA;oBAAKgH,SAAS,EAAC,0HAA0H;oBAAAD,QAAA,eACvI/G,OAAA,CAACJ,IAAI;sBAACoH,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACNrH,OAAA;oBAAIgH,SAAS,EAAC,gGAAgG;oBAAAD,QAAA,EAAC;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENrH,OAAA;gBAAKgH,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,gBAE1C/G,OAAA,CAACb,sBAAsB;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAG1BrH,OAAA;kBAAKgH,SAAS,EAAC,6JAA6J;kBAAAD,QAAA,gBAChL/G,OAAA;oBACQsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,WAAW,CAAE;oBAC1CsG,SAAS,EAAE,iHACTvG,UAAU,KAAK,WAAW,GACtB,6HAA6H,GAC7H,sHAAsH,EACzH;oBAAAsG,QAAA,gBAEH/G,OAAA,CAACJ,IAAI;sBAACoH,SAAS,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChCrH,OAAA;sBAAA+G,QAAA,EAAM;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACHrH,OAAA;oBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,eAAe,CAAE;oBAC9CsG,SAAS,EAAE,iHACTvG,UAAU,KAAK,eAAe,GAC1B,+HAA+H,GAC/H,sHAAsH,EACzH;oBAAAsG,QAAA,gBAEH/G,OAAA,CAACF,GAAG;sBAACkH,SAAS,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BrH,OAAA;sBAAA+G,QAAA,EAAM;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACTrH,OAAA;oBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,YAAY,CAAE;oBAC3CsG,SAAS,EAAE,iHACTvG,UAAU,KAAK,YAAY,GACvB,8HAA8H,GAC9H,sHAAsH,EACzH;oBAAAsG,QAAA,gBAEH/G,OAAA,CAACN,UAAU;sBAACsH,SAAS,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtCrH,OAAA;sBAAA+G,QAAA,EAAM;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAELlH,QAAQ,CAACqE,MAAM,KAAK,CAAC,iBACpBxE,OAAA;kBACEsH,OAAO,EAAET,YAAa;kBACtBG,SAAS,EAAC,0XAA0X;kBACpY/E,KAAK,EAAC,wCAAmC;kBAAA8E,QAAA,gBAEzC/G,OAAA,CAACH,QAAQ;oBAACmH,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnDrH,OAAA;oBAAA+G,QAAA,EAAM;kBAAI;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CACT,eAEDrH,OAAA;kBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,WAAW,CAAE;kBAC1CsG,SAAS,EAAC,wVAAwV;kBAAAD,QAAA,gBAElW/G,OAAA,CAACL,IAAI;oBAACqH,SAAS,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/CrH,OAAA;oBAAMgH,SAAS,EAAC,gBAAgB;oBAAAD,QAAA,EAAC;kBAAe;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAOTrH,OAAA;UAAMgH,SAAS,EAAC,qCAAqC;UAAAD,QAAA,eACnD/G,OAAA;YAAKgH,SAAS,EAAC,6DAA6D;YAAAD,QAAA,gBAG1E/G,OAAA;cAAKgH,SAAS,EAAC,eAAe;cAAAD,QAAA,GAC3BtG,UAAU,KAAK,WAAW,iBACzBT,OAAA,CAACX,cAAc;gBACX4B,cAAc,EAAEA,cAAe;gBACjCZ,cAAc,EAAEA,cAAe;gBAC/BkH,gBAAgB,EAAEvF,oBAAqB;gBACvCwF,eAAe,EAAEzE,mBAAoB;gBACrC0E,gBAAgB,EAAErG,cAAe;gBACjCsG,iBAAiB,EAAErG;cAAgB;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CACF,EAEA5G,UAAU,KAAK,eAAe,iBAC7BT,OAAA,CAACZ,oBAAoB;gBACnBe,QAAQ,EAAEA,QAAS;gBACnBwH,oBAAoB,EAAEnE,wBAAyB;gBAC/CoE,qBAAqB,EAAE7C,yBAA0B;gBACjDxD,cAAc,EAAEA,cAAe;gBAC/BM,sBAAsB,EAAEA;cAAuB;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CACF,EAEA5G,UAAU,KAAK,YAAY,iBAC1BT,OAAA,CAACV,0BAA0B;gBACzBa,QAAQ,EAAEA,QAAS;gBACnB0H,aAAa,EAAE7C,iBAAkB;gBACjC8C,eAAe,EAAElC;cAAoB;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGJrH,OAAA;cAAKgH,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC9B/G,OAAA,CAACf,YAAY;gBACXkB,QAAQ,EAAEA,QAAS;gBACjBE,cAAc,EAAEA,cAAe;gBACjC0H,eAAe,EAAEtC,mBAAoB;gBACrCuC,eAAe,EAAEtC,mBAAoB;gBACrCoC,eAAe,EAAElC,mBAAoB;gBACnCqC,WAAW,EAAEhH,cAAc,CAACgH;cAAY;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACnH,EAAA,CAnaQD,GAAG;EAAA,QAWiFV,gBAAgB,EAYvGC,gBAAgB;AAAA;AAAA0I,EAAA,GAvBbjI,GAAG;AAqaZ,eAAeA,GAAG;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}