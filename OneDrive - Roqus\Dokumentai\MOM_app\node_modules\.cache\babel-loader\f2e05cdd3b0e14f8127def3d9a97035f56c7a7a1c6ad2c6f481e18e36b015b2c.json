{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\RecordingPanel.tsx\";\nimport React from 'react';\nimport { Plus, Mic2, Square } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const RecordingPanel = ({\n  recordingState,\n  onStartRecording,\n  onStopRecording\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-1 flex flex-col justify-center items-center space-y-6 p-4 animate-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-24 h-24 sm:w-32 sm:h-32 rounded-full flex items-center justify-center shadow-2xl transition-all duration-700 ease-out transform ${recordingState.isRecording ? 'bg-gradient-to-br from-red-500/80 to-red-600/80 animate-pulse scale-110' : 'bg-gradient-to-br from-blue-500/80 to-indigo-600/80 scale-100 hover:scale-105'}`,\n        children: /*#__PURE__*/_jsxDEV(Mic2, {\n          className: `h-12 w-12 sm:h-16 sm:w-16 text-white transition-all duration-500 ${recordingState.isRecording ? 'animate-bounce' : 'hover:scale-110'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2 animate-fade-in-up\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl sm:text-2xl font-bold text-white transition-all duration-300\",\n          children: recordingState.isRecording ? 'Įrašoma...' : 'Pasiruošęs įrašyti'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm sm:text-base text-white/70 max-w-md transition-all duration-300\",\n          children: recordingState.isRecording ? 'Spauskite \"Sustabdyti\" norėdami baigti įrašymą' : 'Spauskite \"Naujas pokalbis\" norėdami pradėti naują audio įrašymą su automatine transkribavimo galimybe.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-200\",\n      children: !recordingState.isRecording ? /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`),\n        className: \"inline-flex items-center justify-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95\",\n        children: [/*#__PURE__*/_jsxDEV(Plus, {\n          className: \"h-5 w-5 transition-transform duration-200 group-hover:rotate-90\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Naujas pokalbis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onStopRecording,\n        className: \"inline-flex items-center justify-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-red-500/80 via-red-600/70 to-red-700/80 hover:from-red-500/90 hover:via-red-600/80 hover:to-red-700/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-red-400/40 hover:border-red-300/50 transform hover:scale-105 active:scale-95\",\n        children: [/*#__PURE__*/_jsxDEV(Square, {\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Sustabdyti \\u012Fra\\u0161ym\\u0105\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), recordingState.isRecording && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 text-red-400 animate-fade-in-up animation-delay-400\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-2 h-2 bg-red-400 rounded-full animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-medium\",\n        children: \"\\u012Era\\u0161oma...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = RecordingPanel;\nvar _c;\n$RefreshReg$(_c, \"RecordingPanel\");", "map": {"version": 3, "names": ["React", "Plus", "Mic2", "Square", "jsxDEV", "_jsxDEV", "RecordingPanel", "recordingState", "onStartRecording", "onStopRecording", "className", "children", "isRecording", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "Date", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';\r\nimport { RecordingButton } from './RecordingButton';\r\nimport { RecordingIndicator } from './RecordingIndicator';\r\nimport { Meeting, RecordingState } from '../types/meeting';\r\n\r\ninterface RecordingPanelProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nexport const RecordingPanel: React.FC<RecordingPanelProps> = ({\r\n  recordingState,\r\n  onStartRecording,\r\n  onStopRecording,\r\n}) => {\r\n  return (\r\n    <div className=\"flex-1 flex flex-col justify-center items-center space-y-6 p-4 animate-fade-in\">\r\n      {/* Recording Status Display */}\r\n      <div className=\"text-center space-y-4\">\r\n        <div className={`w-24 h-24 sm:w-32 sm:h-32 rounded-full flex items-center justify-center shadow-2xl transition-all duration-700 ease-out transform ${\r\n          recordingState.isRecording \r\n            ? 'bg-gradient-to-br from-red-500/80 to-red-600/80 animate-pulse scale-110' \r\n            : 'bg-gradient-to-br from-blue-500/80 to-indigo-600/80 scale-100 hover:scale-105'\r\n        }`}>\r\n          <Mic2 className={`h-12 w-12 sm:h-16 sm:w-16 text-white transition-all duration-500 ${\r\n            recordingState.isRecording ? 'animate-bounce' : 'hover:scale-110'\r\n          }`} />\r\n        </div>\r\n        \r\n        <div className=\"space-y-2 animate-fade-in-up\">\r\n          <h3 className=\"text-xl sm:text-2xl font-bold text-white transition-all duration-300\">\r\n            {recordingState.isRecording ? 'Įrašoma...' : 'Pasiruošęs įrašyti'}\r\n          </h3>\r\n          <p className=\"text-sm sm:text-base text-white/70 max-w-md transition-all duration-300\">\r\n            {recordingState.isRecording \r\n              ? 'Spauskite \"Sustabdyti\" norėdami baigti įrašymą'\r\n              : 'Spauskite \"Naujas pokalbis\" norėdami pradėti naują audio įrašymą su automatine transkribavimo galimybe.'\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recording Controls */}\r\n      <div className=\"flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-200\">\r\n        {!recordingState.isRecording ? (\r\n          <button\r\n            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}\r\n            className=\"inline-flex items-center justify-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95\"\r\n          >\r\n            <Plus className=\"h-5 w-5 transition-transform duration-200 group-hover:rotate-90\" />\r\n            <span>Naujas pokalbis</span>\r\n          </button>\r\n        ) : (\r\n          <button\r\n            onClick={onStopRecording}\r\n            className=\"inline-flex items-center justify-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-red-500/80 via-red-600/70 to-red-700/80 hover:from-red-500/90 hover:via-red-600/80 hover:to-red-700/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-red-400/40 hover:border-red-300/50 transform hover:scale-105 active:scale-95\"\r\n          >\r\n            <Square className=\"h-5 w-5\" />\r\n            <span>Sustabdyti įrašymą</span>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recording Indicator */}\r\n      {recordingState.isRecording && (\r\n        <div className=\"flex items-center space-x-2 text-red-400 animate-fade-in-up animation-delay-400\">\r\n          <div className=\"w-2 h-2 bg-red-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"text-sm font-medium\">Įrašoma...</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": ";AAAA,OAAOA,KAAK,MAAiC,OAAO;AACpD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAkC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc5E,OAAO,MAAMC,cAA6C,GAAGA,CAAC;EAC5DC,cAAc;EACdC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EACJ,oBACEJ,OAAA;IAAKK,SAAS,EAAC,gFAAgF;IAAAC,QAAA,gBAE7FN,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCN,OAAA;QAAKK,SAAS,EAAE,qIACdH,cAAc,CAACK,WAAW,GACtB,yEAAyE,GACzE,+EAA+E,EAClF;QAAAD,QAAA,eACDN,OAAA,CAACH,IAAI;UAACQ,SAAS,EAAE,oEACfH,cAAc,CAACK,WAAW,GAAG,gBAAgB,GAAG,iBAAiB;QAChE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENX,OAAA;QAAKK,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CN,OAAA;UAAIK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EACjFJ,cAAc,CAACK,WAAW,GAAG,YAAY,GAAG;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACLX,OAAA;UAAGK,SAAS,EAAC,yEAAyE;UAAAC,QAAA,EACnFJ,cAAc,CAACK,WAAW,GACvB,gDAAgD,GAChD;QAAyG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE5G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNX,OAAA;MAAKK,SAAS,EAAC,wEAAwE;MAAAC,QAAA,EACpF,CAACJ,cAAc,CAACK,WAAW,gBAC1BP,OAAA;QACEY,OAAO,EAAEA,CAAA,KAAMT,gBAAgB,CAAC,YAAY,IAAIU,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAE;QAClFT,SAAS,EAAC,uZAAuZ;QAAAC,QAAA,gBAEjaN,OAAA,CAACJ,IAAI;UAACS,SAAS,EAAC;QAAiE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFX,OAAA;UAAAM,QAAA,EAAM;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,gBAETX,OAAA;QACEY,OAAO,EAAER,eAAgB;QACzBC,SAAS,EAAC,2YAA2Y;QAAAC,QAAA,gBAErZN,OAAA,CAACF,MAAM;UAACO,SAAS,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9BX,OAAA;UAAAM,QAAA,EAAM;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLT,cAAc,CAACK,WAAW,iBACzBP,OAAA;MAAKK,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC9FN,OAAA;QAAKK,SAAS,EAAC;MAA+C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrEX,OAAA;QAAMK,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACI,EAAA,GA9DWd,cAA6C;AAAA,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}