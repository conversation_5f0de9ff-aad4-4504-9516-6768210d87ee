{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\WhisperStatusIndicator.tsx\";\nimport React from 'react';\nimport { CheckCircle2, XCircle, Zap } from 'lucide-react';\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const WhisperStatusIndicator = () => {\n  const isConfigured = isWhisperConfigured();\n  const status = getConfigStatus();\n  const getStatusConfig = () => {\n    if (isConfigured) {\n      return {\n        icon: /*#__PURE__*/_jsxDEV(CheckCircle2, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 15\n        }, this),\n        text: 'Whisper API',\n        color: 'text-emerald-300',\n        bgColor: 'bg-gradient-to-r from-emerald-500/20 via-green-500/15 to-emerald-500/20',\n        borderColor: 'border-emerald-400/30',\n        dotColor: 'bg-gradient-to-r from-emerald-400 to-green-400'\n      };\n    } else {\n      return {\n        icon: /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 15\n        }, this),\n        text: 'API nėra',\n        color: 'text-amber-300',\n        bgColor: 'bg-gradient-to-r from-amber-500/20 via-yellow-500/15 to-amber-500/20',\n        borderColor: 'border-amber-400/30',\n        dotColor: 'bg-gradient-to-r from-amber-400 to-orange-400'\n      };\n    }\n  };\n  const config = getStatusConfig();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `inline-flex items-center space-x-2 px-4 py-2 rounded-xl border backdrop-blur-md transition-all duration-300 shadow-lg hover:shadow-xl ${config.bgColor} ${config.borderColor}`,\n    title: isConfigured ? `OpenAI Whisper API sukonfigūruotas\\nModelis: ${status.model}\\nKalba: ${status.language}\\nMaks. failas: ${status.maxFileSize}` : 'OpenAI Whisper API nesukonfigūruotas. Pridėkite API raktą į .env failą',\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-2.5 h-2.5 rounded-full ${config.dotColor} ${isConfigured ? 'animate-pulse' : ''}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Zap, {\n      className: `h-4 w-4 ${config.color}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `text-sm font-medium ${config.color}`,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: config.color,\n      children: config.icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_c = WhisperStatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"WhisperStatusIndicator\");", "map": {"version": 3, "names": ["React", "CheckCircle2", "XCircle", "Zap", "isWhisperConfigured", "getConfigStatus", "jsxDEV", "_jsxDEV", "WhisperStatusIndicator", "isConfigured", "status", "getStatusConfig", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "color", "bgColor", "borderColor", "dotColor", "config", "title", "model", "language", "maxFileSize", "children", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperStatusIndicator.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { CheckCircle2, XCircle, Alert<PERSON>riangle, Zap } from 'lucide-react';\r\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\r\n\r\nexport const WhisperStatusIndicator: React.FC = () => {\r\n  const isConfigured = isWhisperConfigured();\r\n  const status = getConfigStatus();\r\n\r\n  const getStatusConfig = () => {\r\n    if (isConfigured) {\r\n      return {\r\n        icon: <CheckCircle2 className=\"h-4 w-4\" />,\r\n        text: 'Whisper API',\r\n        color: 'text-emerald-300',\r\n        bgColor: 'bg-gradient-to-r from-emerald-500/20 via-green-500/15 to-emerald-500/20',\r\n        borderColor: 'border-emerald-400/30',\r\n        dotColor: 'bg-gradient-to-r from-emerald-400 to-green-400'\r\n      };\r\n    } else {\r\n      return {\r\n        icon: <XCircle className=\"h-4 w-4\" />,\r\n        text: 'API nėra',\r\n        color: 'text-amber-300',\r\n        bgColor: 'bg-gradient-to-r from-amber-500/20 via-yellow-500/15 to-amber-500/20',\r\n        borderColor: 'border-amber-400/30',\r\n        dotColor: 'bg-gradient-to-r from-amber-400 to-orange-400'\r\n      };\r\n    }\r\n  };\r\n\r\n  const config = getStatusConfig();\r\n\r\n  return (\r\n    <div \r\n      className={`inline-flex items-center space-x-2 px-4 py-2 rounded-xl border backdrop-blur-md transition-all duration-300 shadow-lg hover:shadow-xl ${config.bgColor} ${config.borderColor}`}\r\n      title={isConfigured \r\n        ? `OpenAI Whisper API sukonfigūruotas\\nModelis: ${status.model}\\nKalba: ${status.language}\\nMaks. failas: ${status.maxFileSize}`\r\n        : 'OpenAI Whisper API nesukonfigūruotas. Pridėkite API raktą į .env failą'\r\n      }\r\n    >\r\n      {/* Status dot */}\r\n      <div className={`w-2.5 h-2.5 rounded-full ${config.dotColor} ${isConfigured ? 'animate-pulse' : ''}`} />\r\n      \r\n      {/* Whisper icon */}\r\n      <Zap className={`h-4 w-4 ${config.color}`} />\r\n      \r\n      {/* Status text */}\r\n      <span className={`text-sm font-medium ${config.color}`}>\r\n        {config.text}\r\n      </span>\r\n      \r\n      {/* Status icon */}\r\n      <div className={config.color}>\r\n        {config.icon}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,EAAEC,OAAO,EAAiBC,GAAG,QAAQ,cAAc;AACxE,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,OAAO,MAAMC,sBAAgC,GAAGA,CAAA,KAAM;EACpD,MAAMC,YAAY,GAAGL,mBAAmB,CAAC,CAAC;EAC1C,MAAMM,MAAM,GAAGL,eAAe,CAAC,CAAC;EAEhC,MAAMM,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIF,YAAY,EAAE;MAChB,OAAO;QACLG,IAAI,eAAEL,OAAA,CAACN,YAAY;UAACY,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC1CC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,kBAAkB;QACzBC,OAAO,EAAE,yEAAyE;QAClFC,WAAW,EAAE,uBAAuB;QACpCC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLV,IAAI,eAAEL,OAAA,CAACL,OAAO;UAACW,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACrCC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,gBAAgB;QACvBC,OAAO,EAAE,sEAAsE;QAC/EC,WAAW,EAAE,qBAAqB;QAClCC,QAAQ,EAAE;MACZ,CAAC;IACH;EACF,CAAC;EAED,MAAMC,MAAM,GAAGZ,eAAe,CAAC,CAAC;EAEhC,oBACEJ,OAAA;IACEM,SAAS,EAAE,yIAAyIU,MAAM,CAACH,OAAO,IAAIG,MAAM,CAACF,WAAW,EAAG;IAC3LG,KAAK,EAAEf,YAAY,GACf,gDAAgDC,MAAM,CAACe,KAAK,YAAYf,MAAM,CAACgB,QAAQ,mBAAmBhB,MAAM,CAACiB,WAAW,EAAE,GAC9H,wEACH;IAAAC,QAAA,gBAGDrB,OAAA;MAAKM,SAAS,EAAE,4BAA4BU,MAAM,CAACD,QAAQ,IAAIb,YAAY,GAAG,eAAe,GAAG,EAAE;IAAG;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGxGV,OAAA,CAACJ,GAAG;MAACU,SAAS,EAAE,WAAWU,MAAM,CAACJ,KAAK;IAAG;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG7CV,OAAA;MAAMM,SAAS,EAAE,uBAAuBU,MAAM,CAACJ,KAAK,EAAG;MAAAS,QAAA,EACpDL,MAAM,CAACL;IAAI;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGPV,OAAA;MAAKM,SAAS,EAAEU,MAAM,CAACJ,KAAM;MAAAS,QAAA,EAC1BL,MAAM,CAACX;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GArDWrB,sBAAgC;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}