import React, { useState, useCallback } from 'react';
import { 
  Recording<PERSON>utton, 
  RecordingIndicator, 
  TranscriptViewer, 
  MeetingsList, 
  ErrorBoundary, 
  WhisperConfig,
  WhisperStatusIndicator,
  TranscriptionManager,
  ProfessionalTranscriptViewer,
  RecordingPanel,
  CollapsibleTranscriptsList,
  GridControls
} from './components';
import { useAudioRecorder, useTranscription } from './hooks';
import { Meeting, TranscriptionStatus, Speaker } from './types/meeting';
import { createDemoMeetings } from './utils/demoData';
import { identifySpeakers } from './services/speakerService';
import { Headphones, Plus, Mic2, TestTube, Zap, Settings, List } from 'lucide-react';

function App() {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);
  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);
  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');
  
  // Grid customization state
  const [gridSize, setGridSize] = useState(120);
  const [gridRotation, setGridRotation] = useState(0);
  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');

  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();
  const { 
    transcript, 
    isTranscribing, 
    transcribeAudioEnhanced, 
    cancelTranscription,
    editSegment,
    clearTranscript, 
    clearError,
    currentTranscriptionId,
    progress,
    isWhisperConfigured
  } = useTranscription();

  const handleStartRecording = useCallback(async (title: string) => {
    try {
      await startRecording();
      
      const newMeeting: Meeting = {
        id: Date.now().toString(),
        title: title,
        date: new Date(),
        duration: 0,
        status: 'recording',
        transcriptionStatus: {
          state: 'not_started',
        },
      };
      
      setCurrentMeeting(newMeeting);
      setMeetings(prev => [newMeeting, ...prev]);
      setActiveView('recording');
      clearTranscript();
    } catch (error) {
      console.error('Nepavyko pradėti įrašymo:', error);
      throw error;
    }
  }, [startRecording, clearTranscript]);

  const handleStopRecording = useCallback(async () => {
    try {
      const audioBlob = await stopRecording();
      
      if (currentMeeting && audioBlob) {
        const updatedMeeting: Meeting = {
          ...currentMeeting,
          status: 'completed',
          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),
          audioBlob,
          transcriptionStatus: {
            state: 'not_started',
          },
        };

        setCurrentMeeting(updatedMeeting);
        setMeetings(prev => 
          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)
        );

        // Switch to transcription management view
        setActiveView('transcription');
      }
    } catch (error) {
      console.error('Nepavyko sustabdyti įrašymo:', error);
      alert('Nepavyko sustabdyti įrašymo.');
    }
  }, [stopRecording, currentMeeting]);

  const handleStartTranscription = useCallback(async (meetingId: string) => {
    const meeting = meetings.find(m => m.id === meetingId);
    if (!meeting || !meeting.audioBlob) return;

    // Update meeting status to pending
    const updatedMeeting: Meeting = {
      ...meeting,
      transcriptionStatus: {
        state: 'pending',
        startedAt: new Date(),
      },
    };

    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));
    setSelectedMeetingForTranscript(updatedMeeting);

    try {
      // Start professional transcription
      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {
        onProgress: (progress) => {
          setMeetings(prev => prev.map(m => 
            m.id === meetingId 
              ? { 
                  ...m, 
                  transcriptionStatus: { 
                    ...m.transcriptionStatus, 
                    progress,
                    state: 'processing' 
                  } 
                }
              : m
          ));
        },
        onStatusUpdate: (status) => {
          setMeetings(prev => prev.map(m => 
            m.id === meetingId 
              ? { 
                  ...m, 
                  transcriptionStatus: status 
                }
              : m
          ));
        },
        enhanceSpeakers: true,
      });

      // Update meeting with completed transcription
      const completedMeeting: Meeting = {
        ...updatedMeeting,
        transcript: result.segments,
        participants: result.speakers,
        metadata: result.metadata,
        transcriptionStatus: {
          state: 'completed',
          progress: 100,
          startedAt: updatedMeeting.transcriptionStatus.startedAt,
          completedAt: new Date(),
        },
      };

      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));
      setSelectedMeetingForTranscript(completedMeeting);
      
      // Automatiškai pereiti į rezultatų puslapį
      setActiveView('transcript');

      console.log('✅ Transkribavimas sėkmingai baigtas:', {
        segments: result.segments.length,
        speakers: result.speakers.length,
        words: result.metadata.totalWords,
        confidence: result.metadata.averageConfidence,
      });

    } catch (error: any) {
      console.error('❌ Transkribavimo klaida:', error);
      
      const errorMeeting: Meeting = {
        ...updatedMeeting,
        transcriptionStatus: {
          state: 'failed',
          error: error.message,
          startedAt: updatedMeeting.transcriptionStatus.startedAt,
        },
      };

      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));
    }
  }, [meetings, transcribeAudioEnhanced]);

  const handleCancelTranscription = useCallback((meetingId: string) => {
    cancelTranscription();
    
    setMeetings(prev => prev.map(m => 
      m.id === meetingId 
        ? { 
            ...m, 
            transcriptionStatus: { 
              ...m.transcriptionStatus, 
              state: 'cancelled' as const 
            } 
          }
        : m
    ));
  }, [cancelTranscription]);

  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {
    editSegment(segmentId, newText);
    
    // Update the meeting's transcript
    setMeetings(prev => prev.map(meeting => 
      meeting.id === meetingId
        ? {
            ...meeting,
            transcript: meeting.transcript?.map(segment => 
              segment.id === segmentId 
                ? {
                    ...segment,
                    text: newText,
                    isEdited: true,
                    editedAt: new Date(),
                    editedBy: 'user'
                  }
                : segment
            ),
          }
        : meeting
    ));
  }, [editSegment]);

  const handleSelectMeeting = useCallback((meeting: Meeting) => {
    setCurrentMeeting(meeting);
    if (meeting.transcript && meeting.transcript.length > 0) {
      setSelectedMeetingForTranscript(meeting);
      setActiveView('transcript');
    }
  }, []);

  const handleDeleteMeeting = useCallback((meetingId: string) => {
    setMeetings(prev => prev.filter(m => m.id !== meetingId));
    if (currentMeeting?.id === meetingId) {
      setCurrentMeeting(null);
    }
    if (selectedMeetingForTranscript?.id === meetingId) {
      setSelectedMeetingForTranscript(null);
    }
  }, [currentMeeting, selectedMeetingForTranscript]);

  const handleExportMeeting = useCallback((meeting: Meeting) => {
    const exportData = {
      title: meeting.title,
      date: meeting.date.toISOString(),
      duration: meeting.duration,
      transcript: meeting.transcript || transcript,
      participants: meeting.participants || [],
      metadata: meeting.metadata || {},
      transcriptionStatus: meeting.transcriptionStatus,
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `meeting-${meeting.title.replace(/\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, [transcript]);



  const loadDemoData = useCallback(() => {
    const demoMeetings = createDemoMeetings().map(meeting => ({
      ...meeting,
      transcriptionStatus: {
        state: 'completed' as const,
        progress: 100,
        completedAt: meeting.date,
      },
    }));
    setMeetings(demoMeetings);
    setActiveView('transcript');
  }, []);

  return (
    <ErrorBoundary>
      <div
        className={`min-h-screen bg-app-gradient font-inter relative overflow-hidden ${
          activeView === 'recording' ? 'grid-page-recording' :
          activeView === 'transcription' ? 'grid-page-transcription' :
          activeView === 'transcript' ? 'grid-page-results' :
          'grid-page-home'
        }`}
        style={{
          '--grid-size': `${gridSize}px`,
          '--grid-rotation': `${gridRotation}deg`,
          '--grid-color': gridColor,
        } as React.CSSProperties}
      >
        {/* Content wrapper */}
        <div className="relative z-10">
          {/* Elegant Modern Header */}
        <header className="fixed top-0 left-0 right-0 z-50">
          <div className="elegant-navbar-glass border-b border-white/5">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between h-16">

                {/* Logo and Brand Section */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-3">
                    <div
                      className="elegant-logo-container group"
                      data-tooltip="MOM - Meeting Recording App"
                    >
                      <Mic2 className="h-5 w-5 text-white group-hover:text-blue-200 transition-colors duration-300" />
                    </div>
                    <div className="hidden sm:block">
                      <h1 className="text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                        MOM App
                      </h1>
                      <p className="text-xs text-white/60 font-medium tracking-wide">
                        Meeting Recording & Transcription
                      </p>
                    </div>
                    <div className="sm:hidden">
                      <h1 className="text-lg font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                        MOM
                      </h1>
                    </div>
                  </div>
                </div>

                {/* Center Navigation Section */}
                <div className="flex items-center gap-6">
                  {/* Whisper Status with enhanced styling */}
                  <div className="elegant-status-container">
                    <WhisperStatusIndicator />
                  </div>

                  {/* Elegant Navigation Pills */}
                  <nav className="elegant-nav-container">
                    <div className="elegant-nav-pills">
                      <button
                        onClick={() => setActiveView('recording')}
                        className={`elegant-nav-pill ${
                          activeView === 'recording' ? 'active' : ''
                        }`}
                        data-tooltip="Įrašyti naują pokalbį"
                      >
                        <Mic2 className="h-4 w-4" />
                        <span className="hidden md:inline font-medium">Įrašymas</span>
                      </button>
                      <button
                        onClick={() => setActiveView('transcription')}
                        className={`elegant-nav-pill ${
                          activeView === 'transcription' ? 'active' : ''
                        }`}
                        data-tooltip="Transkribuoti audio failus"
                      >
                        <Zap className="h-4 w-4" />
                        <span className="hidden md:inline font-medium">Transkribavimas</span>
                      </button>
                      <button
                        onClick={() => setActiveView('transcript')}
                        className={`elegant-nav-pill ${
                          activeView === 'transcript' ? 'active' : ''
                        }`}
                        data-tooltip="Peržiūrėti rezultatus ir transkriptus"
                      >
                        <Headphones className="h-4 w-4" />
                        <span className="hidden md:inline font-medium">Rezultatai</span>
                      </button>
                    </div>
                  </nav>
                </div>

                {/* Action Buttons Section */}
                <div className="flex items-center gap-3">
                  {meetings.length === 0 && (
                    <button
                      onClick={loadDemoData}
                      className="elegant-demo-button group"
                      data-tooltip="Užkrauti demonstracinius duomenis testavimui"
                    >
                      <TestTube className="h-4 w-4 group-hover:rotate-12 transition-transform duration-300" />
                      <span className="hidden lg:inline font-medium">Demo</span>
                    </button>
                  )}

                  {/* Primary CTA Button */}
                  <button
                    onClick={() => {
                      setActiveView('recording');
                      if (!recordingState.isRecording) {
                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);
                      }
                    }}
                    className="elegant-primary-button group"
                    data-tooltip="Pradėti naują pokalbio įrašymą"
                  >
                    <Plus className="h-4 w-4 group-hover:rotate-90 transition-transform duration-300" />
                    <span className="hidden lg:inline font-semibold">Naujas pokalbis</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </header>





        {/* Modern Main Content */}
        <main className="pt-20 px-4 sm:px-6 lg:px-8 pb-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 min-h-[calc(100vh-140px)]">

              {/* Main Content Area */}
              <div className="xl:col-span-3">
                <div className="relative h-full">
                  {/* Recording View */}
                  <div className={`absolute inset-0 transition-all duration-300 ease-out ${
                    activeView === 'recording'
                      ? 'opacity-100 translate-x-0'
                      : 'opacity-0 -translate-x-4 pointer-events-none'
                  }`}>
                    <div className="card h-full flex flex-col" style={{
                      padding: 'var(--space-6)',
                      background: 'var(--color-bg-elevated)',
                      borderRadius: 'var(--radius-xl)'
                    }}>
                      {/* Header */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style={{
                            background: 'linear-gradient(135deg, var(--color-accent-primary), var(--color-accent-hover))'
                          }}>
                            <Mic2 className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <h2 className="text-2xl font-semibold text-primary">Pokalbio įrašymas</h2>
                            <p className="text-sm text-secondary">Pradėkite naują pokalbio įrašymą arba tęskite esamą</p>
                          </div>
                        </div>
                        <button
                          onClick={() => {
                            if (!recordingState.isRecording) {
                              handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);
                            }
                          }}
                          className="tooltip btn btn-primary hidden sm:flex"
                          data-tooltip="Pradėti naują pokalbio įrašymą"
                        >
                          <Plus className="h-4 w-4" />
                          <span>Naujas pokalbis</span>
                        </button>
                      </div>

                      {/* Content */}
                      <div className="flex-1 flex flex-col">
                        <RecordingPanel
                          recordingState={recordingState}
                          currentMeeting={currentMeeting}
                          onStartRecording={handleStartRecording}
                          onStopRecording={handleStopRecording}
                        onPauseRecording={() => {}}
                        onResumeRecording={() => {}}
                      />
                    </div>
                  </div>
                </div>

                {/* Transcription View */}
                <div className={`absolute inset-0 transition-all duration-300 ease-out ${
                  activeView === 'transcription'
                    ? 'opacity-100 translate-x-0'
                    : 'opacity-0 translate-x-4 pointer-events-none'
                }`}>
                  <div className="card h-full flex flex-col" style={{
                    padding: 'var(--space-6)',
                    background: 'var(--color-bg-elevated)',
                    borderRadius: 'var(--radius-xl)'
                  }}>
                    {/* Header */}
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style={{
                        background: 'linear-gradient(135deg, #8b5cf6, #a855f7)'
                      }}>
                        <Zap className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-semibold text-primary">Transkribavimas</h2>
                        <p className="text-sm text-secondary">Audio failų konvertavimas į tekstą naudojant AI</p>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 flex flex-col">
                      <TranscriptionManager
                        meetings={meetings}
                        onStartTranscription={handleStartTranscription}
                        onCancelTranscription={handleCancelTranscription}
                        isTranscribing={isTranscribing}
                        currentTranscriptionId={currentTranscriptionId}
                        onDeleteMeeting={handleDeleteMeeting}
                        onViewResults={() => setActiveView('transcript')}
                      />
                    </div>
                  </div>
                </div>

                {/* Transcript View */}
                <div className={`absolute inset-0 transition-all duration-300 ease-out ${
                  activeView === 'transcript'
                    ? 'opacity-100 translate-x-0'
                    : 'opacity-0 translate-x-4 pointer-events-none'
                }`}>
                  <div className="card h-full flex flex-col" style={{
                    padding: 'var(--space-6)',
                    background: 'var(--color-bg-elevated)',
                    borderRadius: 'var(--radius-xl)'
                  }}>
                    {/* Header */}
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg" style={{
                        background: 'linear-gradient(135deg, #10b981, #059669)'
                      }}>
                        <Headphones className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-semibold text-primary">Rezultatai</h2>
                        <p className="text-sm text-secondary">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 flex flex-col">
                      <ProfessionalTranscriptViewer
                        meetings={meetings}
                        onDeleteMeeting={handleDeleteMeeting}
                        onGoToTranscription={() => setActiveView('transcription')}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar - Meetings List */}
            <div className="xl:col-span-1">
              <div className="card h-full flex flex-col" style={{
                padding: 'var(--space-6)',
                background: 'var(--color-bg-elevated)',
                borderRadius: 'var(--radius-xl)'
              }}>
                {/* Sidebar Header */}
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-10 h-10 rounded-lg flex items-center justify-center shadow-md" style={{
                    background: 'linear-gradient(135deg, #6366f1, #8b5cf6)'
                  }}>
                    <List className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-primary">Pokalbiai</h2>
                    <p className="text-xs text-tertiary">Visi jūsų pokalbiai ({meetings.length})</p>
                  </div>
                </div>

                {/* Sidebar Content */}
                <div className="flex-1 flex flex-col">
                  <MeetingsList
                    meetings={meetings}
                    currentMeeting={currentMeeting}
                    onSelectMeeting={handleSelectMeeting}
                    onDeleteMeeting={handleDeleteMeeting}
                    onExportMeeting={() => {}}
                    activeView="list"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Grid Controls */}
          <GridControls
            onGridSizeChange={setGridSize}
            onGridRotationChange={setGridRotation}
            onGridColorChange={setGridColor}
            currentSize={gridSize}
            currentRotation={gridRotation}
            currentColor={gridColor}
          />
        </main>
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default App; 