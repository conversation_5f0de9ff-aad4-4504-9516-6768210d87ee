import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Calendar, Clock, Users, FileText, CheckCircle2, AlertCircle, Loader2, Download, Edit3, Play, Volume2 } from 'lucide-react';
import { Meeting, Speaker } from '../types/meeting';
import { ProfessionalTranscriptViewer } from './ProfessionalTranscriptViewer';

interface CollapsibleTranscriptsListProps {
  meetings: Meeting[];
  onEditSegment?: (meetingId: string, segmentId: any, newText: any) => void;
  onExportMeeting: (meeting: Meeting) => void;
}

export const CollapsibleTranscriptsList: React.FC<CollapsibleTranscriptsListProps> = ({
  meetings,
  onEditSegment,
  onExportMeeting,
}) => {
  const [expandedMeeting, setExpandedMeeting] = useState<string | null>(null);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const hrs = Math.floor(mins / 60);
    const remainingMins = mins % 60;
    
    if (hrs > 0) {
      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
    }
    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;
  };

  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return `Šiandien, ${date.toLocaleTimeString('lt-LT', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })}`;
    } else if (diffInHours < 48) {
      return `Vakar, ${date.toLocaleTimeString('lt-LT', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })}`;
    } else if (diffInHours < 168) { // 7 dienos
      return date.toLocaleDateString('lt-LT', { 
        weekday: 'long',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('lt-LT', { 
        month: 'long', 
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  const getStatusIcon = (meeting: Meeting) => {
    const status = meeting.transcriptionStatus.state;
    
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (meeting: Meeting) => {
    const status = meeting.transcriptionStatus.state;
    
    switch (status) {
      case 'completed':
        return 'Baigtas';
      case 'processing':
        return `Apdorojama${meeting.transcriptionStatus.progress ? ` (${meeting.transcriptionStatus.progress}%)` : ''}`;
      case 'failed':
        return 'Nepavyko';
      case 'pending':
        return 'Eilėje';
      case 'not_started':
        return meeting.audioBlob ? 'Galima transkribuoti' : 'Nėra audio';
      default:
        return 'Nežinomas statusas';
    }
  };

  const getSpeakersSummary = (meeting: Meeting) => {
    if (!meeting.participants || meeting.participants.length === 0) {
      return 'Kalbėtojai neidentifikuoti';
    }
    
    if (meeting.participants.length <= 2) {
      return meeting.participants.map(p => p.name).join(', ');
    }
    
    return `${meeting.participants[0].name} ir ${meeting.participants.length - 1} kiti`;
  };

  const getTranscriptStats = (meeting: Meeting) => {
    if (!meeting.transcript || meeting.transcript.length === 0) {
      return { segments: 0, words: 0, confidence: 0 };
    }
    
    const segments = meeting.transcript.length;
    const words = meeting.transcript.reduce((sum, seg) => 
      sum + (seg.wordsCount || seg.text.split(' ').length), 0);
    const confidence = meeting.transcript.reduce((sum, seg) => 
      sum + (seg.confidence || 0), 0) / segments;
    
    return { segments, words, confidence: Math.round(confidence * 100) };
  };

  const toggleExpanded = (meetingId: string) => {
    setExpandedMeeting(expandedMeeting === meetingId ? null : meetingId);
  };

  const hasTranscript = (meeting: Meeting) => {
    return meeting.transcriptionStatus.state === 'completed' && 
           meeting.transcript && 
           meeting.transcript.length > 0;
  };

  // Filter meetings that have been completed (audio recorded)
  const completedMeetings = meetings.filter(meeting => 
    meeting.status === 'completed' || meeting.status === 'processing'
  ).sort((a, b) => b.date.getTime() - a.date.getTime());

  if (completedMeetings.length === 0) {
    return (
      <div className="space-y-6">
        {/* Empty State with Better Styling */}
        <div className="gradient-border-fade rounded-3xl p-12 text-center shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary">
          <div className="w-20 h-20 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mx-auto mb-6 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft">
            <FileText className="h-10 w-10 text-blue-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-3">Nėra pokalbių</h2>
          <p className="text-gray-600 max-w-md mx-auto mb-6">
            Sukurkite ir užbaikite pokalbio įrašymą, kad pamatytumėte transkribavimo rezultatus čia.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
            <div className="flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border">
              <span>🎤</span>
              <span>Įrašykite pokalbį</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border">
              <span>⚡</span>
              <span>Transkribuokite su Whisper</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border">
              <span>📊</span>
              <span>Peržiūrėkite rezultatus</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats - Moved to Top */}
      {completedMeetings.length > 0 && (
                 <div className="gradient-border-fade rounded-3xl p-6 shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary hover-gradient-shift float-effect">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Pokalbių apžvalga</h2>
            <div className="text-sm text-gray-500">
              Atnaujinta: {new Date().toLocaleTimeString('lt-LT', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </div>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40">
              <div className="text-3xl font-bold text-blue-600 mb-1">
                {completedMeetings.length}
              </div>
              <div className="text-sm font-medium text-gray-700">
                {completedMeetings.length === 1 ? 'Pokalbis' : 'Pokalbiai'}
              </div>
            </div>
            
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40">
              <div className="text-3xl font-bold text-purple-600 mb-1">
                {completedMeetings.filter(m => hasTranscript(m)).length}
              </div>
              <div className="text-sm font-medium text-gray-700">
                {completedMeetings.filter(m => hasTranscript(m)).length === 1 ? 'Transkribuotas' : 'Transkribuoti'}
              </div>
            </div>
            
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40">
              <div className="text-3xl font-bold text-green-600 mb-1">
                {Math.round(
                  completedMeetings.reduce((sum, m) => sum + m.duration, 0) / 60
                )}min
              </div>
              <div className="text-sm font-medium text-gray-700">Bendra trukmė</div>
            </div>
            
            <div className="bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40">
              <div className="text-3xl font-bold text-orange-600 mb-1">
                {completedMeetings
                  .filter(m => hasTranscript(m))
                  .reduce((sum, m) => sum + getTranscriptStats(m).words, 0)
                  .toLocaleString()
                }
              </div>
              <div className="text-sm font-medium text-gray-700">
                {completedMeetings
                  .filter(m => hasTranscript(m))
                  .reduce((sum, m) => sum + getTranscriptStats(m).words, 0) === 1 ? 'Žodis' : 'Žodžiai'}
              </div>
            </div>
          </div>
          
          {/* Progress Overview */}
          <div className="mt-4 pt-4 border-t border-white/40">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Transkribavimo progresas:</span>
              <span className="font-medium text-gray-800">
                {completedMeetings.length > 0 
                  ? `${Math.round((completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length) * 100)}%`
                  : '0%'
                }
              </span>
            </div>
            <div className="mt-2 w-full bg-white/40 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                style={{ 
                  width: completedMeetings.length > 0 
                    ? `${(completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length) * 100}%`
                    : '0%'
                }}
              />
            </div>
          </div>
        </div>
      )}
      
      {/* Meetings List */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Pokalbių istorija
            {completedMeetings.length > 0 && (
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({completedMeetings.length})
              </span>
            )}
          </h3>
          <div className="text-sm text-gray-500">
            Rūšiuoti pagal datą (naujausi pirma)
          </div>
        </div>
      
        {completedMeetings.map((meeting) => {
          const stats = getTranscriptStats(meeting);
          const isExpanded = expandedMeeting === meeting.id;
          
          return (
                     <div
             key={meeting.id}
             className={`gradient-border-fade rounded-3xl overflow-hidden transition-ultra hover-gradient-shift ${
               isExpanded 
                 ? 'shadow-primary bg-unique-gradient-2 scale-[1.02] pulse-subtle' 
                 : 'shadow-soft bg-unique-gradient-3 hover:shadow-elegant hover:scale-[1.01] float-effect'
             }`}
           >
            {/* Meeting Header - Always Visible */}
                         <div 
               className={`p-6 cursor-pointer transition-smooth ${
                 isExpanded 
                   ? 'bg-gradient-to-r from-blue-50/40 via-purple-50/30 to-indigo-50/40 border-b border-gradient-fade' 
                   : 'hover:bg-gradient-to-r hover:from-white/50 hover:via-blue-50/30 hover:to-white/50'
               }`}
               onClick={() => toggleExpanded(meeting.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1 min-w-0">
                  {/* Expand/Collapse Button */}
                  <button className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors">
                    {isExpanded ? (
                      <ChevronDown className="h-5 w-5" />
                    ) : (
                      <ChevronRight className="h-5 w-5" />
                    )}
                  </button>
                  
                  {/* Meeting Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {meeting.title}
                      </h3>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(meeting)}
                        <span className="text-sm text-gray-600">
                          {getStatusText(meeting)}
                        </span>
                      </div>
                    </div>
                    
                                         <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(meeting.date)}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{formatDuration(meeting.duration)}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{getSpeakersSummary(meeting)}</span>
                      </div>
                      
                      {hasTranscript(meeting) && (
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4" />
                          <span>
                            {stats.words.toLocaleString()} žodžiai 
                            ({stats.confidence}% tikslumas)
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Quick Actions */}
                <div className="flex items-center space-x-2 flex-shrink-0">
                  {meeting.audioBlob && (
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        // Play audio functionality could be added here
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200"
                      title="Klausyti audio"
                    >
                      <Volume2 className="h-4 w-4" />
                    </button>
                  )}
                  
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      onExportMeeting(meeting);
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200"
                    title="Eksportuoti"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
            
                         {/* Expanded Content - Transcript Viewer */}
             {isExpanded && (
               <div className="animate-fadeIn"
                    style={{
                      animation: 'fadeIn 0.3s ease-in-out'
                    }}
               >
                {hasTranscript(meeting) ? (
                  <ProfessionalTranscriptViewer
                    meetings={[meeting]}
                    onDeleteMeeting={() => {}}
                  />
                ) : (
                  <div className="p-8 text-center">
                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      {meeting.transcriptionStatus.state === 'processing' ? (
                        <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
                      ) : meeting.transcriptionStatus.state === 'failed' ? (
                        <AlertCircle className="h-6 w-6 text-red-500" />
                      ) : (
                        <FileText className="h-6 w-6 text-gray-400" />
                      )}
                    </div>
                    
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      {meeting.transcriptionStatus.state === 'processing' 
                        ? 'Apdorojama transkribavimas'
                        : meeting.transcriptionStatus.state === 'failed'
                        ? 'Transkribavimas nepavyko'
                        : 'Nėra transkribavimo'
                      }
                    </h4>
                    
                    <p className="text-sm text-gray-500">
                      {meeting.transcriptionStatus.state === 'processing' 
                        ? `Prašome palaukti... ${meeting.transcriptionStatus.progress || 0}%`
                        : meeting.transcriptionStatus.state === 'failed'
                        ? meeting.transcriptionStatus.error || 'Nežinoma klaida'
                        : meeting.audioBlob
                        ? 'Eikite į transkribavimo skyrių, kad pradėtumėte'
                        : 'Šis pokalbis neturi audio įrašo'
                      }
                    </p>
                    
                    {meeting.transcriptionStatus.state === 'processing' && 
                     meeting.transcriptionStatus.progress && (
                      <div className="mt-4 w-64 bg-gray-200 rounded-full h-2 mx-auto">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${meeting.transcriptionStatus.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
          );
        })}
      </div>
    </div>
  );
}; 