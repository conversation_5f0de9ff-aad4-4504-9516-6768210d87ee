import React, { useState } from 'react';
import { Settings, Grid, RotateCw } from 'lucide-react';

interface GridControlsProps {
  onGridSizeChange: (size: number) => void;
  onGridRotationChange: (rotation: number) => void;
  onGridColorChange: (color: string) => void;
  currentSize: number;
  currentRotation: number;
  currentColor: string;
}

const GridControls: React.FC<GridControlsProps> = ({
  onGridSizeChange,
  onGridRotationChange,
  onGridColorChange,
  currentSize,
  currentRotation,
  currentColor
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const sizeOptions = [40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240];
  const rotationOptions = [0, 15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180];
  const colorOptions = [
    { name: '<PERSON><PERSON><PERSON>', value: 'rgba(196, 181, 253, 0.15)' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 'rgba(147, 51, 234, 0.25)' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 'rgba(147, 51, 234, 0.40)' },
    { name: '<PERSON>sus', value: 'rgba(88, 28, 135, 0.35)' }
  ];

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-gradient-to-r from-white/15 via-white/10 to-white/15 backdrop-blur-xl border border-white/20 rounded-lg sm:rounded-xl p-2 sm:p-3 shadow-2xl hover:shadow-3xl transition-all duration-300"
        title="Tinklelio nustatymai"
      >
        <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-white/80" />
      </button>

      {isOpen && (
        <div className="absolute bottom-16 right-0 bg-gradient-to-br from-white/10 via-white/8 to-white/10 backdrop-blur-2xl border border-white/20 rounded-xl sm:rounded-2xl shadow-2xl p-3 sm:p-4 min-w-[240px] sm:min-w-[280px]">
          <div className="space-y-3 sm:space-y-4">
            <h3 className="text-sm font-semibold text-white/90 flex items-center space-x-2">
                              <Grid className="h-4 w-4" />
              <span>Tinklelio nustatymai</span>
            </h3>

            {/* Grid Size Control */}
            <div>
              <label className="block text-xs font-medium text-white/70 mb-2">
                Tinklelio dydis: {currentSize}px
              </label>
              <div className="grid grid-cols-4 gap-1">
                {sizeOptions.map((size) => (
                  <button
                    key={size}
                    onClick={() => onGridSizeChange(size)}
                    className={`px-2 py-1 text-xs rounded-md transition-all duration-300 ${
                      currentSize === size
                        ? 'bg-gradient-to-r from-blue-500/60 to-indigo-500/60 text-white shadow-lg backdrop-blur-md border border-blue-400/40'
                        : 'bg-white/10 hover:bg-white/20 text-white/80 border border-white/20 backdrop-blur-md'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>

            {/* Grid Rotation Control */}
            <div>
              <label className="block text-xs font-medium text-white/70 mb-2 flex items-center space-x-1">
                <RotateCw className="h-3 w-3" />
                <span>Pasukimas: {currentRotation}°</span>
              </label>
              <div className="grid grid-cols-4 gap-1">
                {rotationOptions.map((rotation) => (
                  <button
                    key={rotation}
                    onClick={() => onGridRotationChange(rotation)}
                    className={`px-2 py-1 text-xs rounded-md transition-all duration-300 ${
                      currentRotation === rotation
                        ? 'bg-gradient-to-r from-purple-500/60 to-pink-500/60 text-white shadow-lg backdrop-blur-md border border-purple-400/40'
                        : 'bg-white/10 hover:bg-white/20 text-white/80 border border-white/20 backdrop-blur-md'
                    }`}
                  >
                    {rotation}°
                  </button>
                ))}
              </div>
            </div>

            {/* Grid Color Control */}
            <div>
              <label className="block text-xs font-medium text-white/70 mb-2">
                Spalva
              </label>
              <div className="grid grid-cols-2 gap-2">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    onClick={() => onGridColorChange(color.value)}
                    className={`px-2 sm:px-3 py-2 text-xs rounded-md transition-all duration-300 border backdrop-blur-md ${
                      currentColor === color.value
                        ? 'bg-gradient-to-r from-green-500/60 to-emerald-500/60 text-white shadow-lg border-green-400/40'
                        : 'bg-white/10 hover:bg-white/20 text-white/80 border-white/20'
                    }`}
                  >
                    {color.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Quick Presets */}
            <div>
              <label className="block text-xs font-medium text-white/70 mb-2">
                Greitieji nustatymai
              </label>
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => {
                    onGridSizeChange(120);
                    onGridRotationChange(0);
                    onGridColorChange('rgba(147, 51, 234, 0.25)');
                  }}
                  className="px-2 sm:px-3 py-2 text-xs bg-gradient-to-r from-blue-500/20 to-indigo-500/20 hover:from-blue-500/30 hover:to-indigo-500/30 text-white/90 rounded-md border border-blue-400/30 backdrop-blur-md transition-all duration-300"
                >
                  Klasikinis
                </button>
                <button
                  onClick={() => {
                    onGridSizeChange(80);
                    onGridRotationChange(45);
                    onGridColorChange('rgba(196, 181, 253, 0.15)');
                  }}
                  className="px-2 sm:px-3 py-2 text-xs bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 text-white/90 rounded-md border border-purple-400/30 backdrop-blur-md transition-all duration-300"
                >
                  Modernus
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GridControls; 