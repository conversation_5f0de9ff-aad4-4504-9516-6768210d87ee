import React, { useState } from 'react';
import { Calendar, Clock, FileText, Trash2, Download, Mic, Square, Loader2, Volume2, List } from 'lucide-react';
import { Meeting } from '../types/meeting';
import { AudioPlayer } from './AudioPlayer';

interface MeetingsListProps {
  meetings: Meeting[];
  currentMeeting: Meeting | null;
  onSelectMeeting: (meeting: Meeting) => void;
  onDeleteMeeting: (meetingId: string) => void;
  onExportMeeting: (meeting: Meeting) => void;
  isRecording?: boolean;
  activeView: 'list' | 'grid';
}

export const MeetingsList: React.FC<MeetingsListProps> = ({
  meetings,
  onSelectMeeting,
  onDeleteMeeting,
  activeView,
}) => {
  const [expandedMeeting, setExpandedMeeting] = useState<string | null>(null);
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'recording':
        return <Mic className="h-3 w-3 text-red-500 animate-pulse" />;
      case 'processing':
        return <Loader2 className="h-3 w-3 text-blue-500 animate-spin" />;
      case 'completed':
        return <FileText className="h-3 w-3 text-green-500" />;
      case 'error':
        return <Square className="h-3 w-3 text-red-500" />;
      default:
        return <FileText className="h-3 w-3 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'recording':
        return 'Įrašoma';
      case 'processing':
        return 'Apdorojama';
      case 'completed':
        return 'Baigta';
      case 'error':
        return 'Klaida';
      default:
        return 'Nežinoma';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recording':
        return 'text-red-500';
      case 'processing':
        return 'text-blue-500';
      case 'completed':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-400';
    }
  };

  if (meetings.length === 0) {
    return (
      <div className="gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 h-full transition-smooth hover:shadow-primary float-effect">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Pokalbiai</h2>
        </div>
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <div className="w-12 h-12 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mb-3 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">Nėra pokalbių</h3>
          <p className="text-xs text-gray-500">Pradėkite naują pokalbį, kad pamatytumėte jį čia</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col animate-fade-in">
      {meetings.length > 0 ? (
        <div className="space-y-4 overflow-y-auto">
          {meetings.map((meeting, index) => (
            <div
              key={meeting.id}
              className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] animate-fade-in-up"
              style={{ animationDelay: `${index * 50}ms` }}
              onClick={() => onSelectMeeting(meeting)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="text-lg font-semibold text-white truncate transition-colors duration-200 mb-2">{meeting.title}</h4>
                  <p className="text-base text-white/70 mb-3 transition-colors duration-200">
                    {meeting.date.toLocaleString('lt-LT')}
                  </p>
                  <div className="flex items-center space-x-4 mb-3 text-sm">
                    <span className="text-white/60 transition-colors duration-200">
                      {Math.floor(meeting.duration / 60)}:{String(meeting.duration % 60).padStart(2, '0')}
                    </span>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(meeting.transcriptionStatus.state)}
                      <span className={`transition-colors duration-200 ${getStatusColor(meeting.transcriptionStatus.state)}`}>
                        {getStatusText(meeting.transcriptionStatus.state)}
                      </span>
                    </div>
                  </div>
                  {meeting.transcript && (
                    <div className="flex items-center space-x-3 text-sm text-green-400 transition-all duration-200">
                      <FileText className="h-4 w-4" />
                      <span>{meeting.transcript.length} segmentų</span>
                      {meeting.participants && (
                        <>
                          <span>•</span>
                          <span>{meeting.participants.length} dalyvių</span>
                        </>
                      )}
                    </div>
                  )}
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteMeeting(meeting.id);
                  }}
                  className="p-3 text-white/40 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 ml-3 transform hover:scale-110"
                  title="Ištrinti pokalbį"
                >
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up">
          <div className="w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center animate-pulse">
            <List className="h-10 w-10 text-indigo-400" />
          </div>
          <div>
            <h3 className="text-xl sm:text-2xl font-semibold text-white">Nėra pokalbių</h3>
            <p className="text-base sm:text-lg text-white/70">
              Pradėkite naują pokalbį, kad pamatytumėte jį čia
            </p>
          </div>
        </div>
      )}
    </div>
  );
};