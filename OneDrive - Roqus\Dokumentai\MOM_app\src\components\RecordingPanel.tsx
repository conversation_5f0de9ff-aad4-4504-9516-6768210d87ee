import React, { useState, useCallback } from 'react';
import { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';
import { RecordingButton } from './RecordingButton';
import { RecordingIndicator } from './RecordingIndicator';
import { Meeting, RecordingState } from '../types/meeting';

interface RecordingPanelProps {
  recordingState: RecordingState;
  currentMeeting: Meeting | null;
  onStartRecording: (title: string) => Promise<void>;
  onStopRecording: () => Promise<void>;
  onPauseRecording: () => void;
  onResumeRecording: () => void;
}

export const RecordingPanel: React.FC<RecordingPanelProps> = ({
  recordingState,
  onStartRecording,
  onStopRecording,
}) => {
  return (
    <div className="flex-1 flex flex-col justify-center items-center space-y-8 p-4 animate-fade-in">
      {/* Recording Status Display */}
      <div className="text-center space-y-6">
        <div className={`w-28 h-28 sm:w-36 sm:h-36 rounded-full flex items-center justify-center shadow-2xl transition-all duration-700 ease-out transform ${
          recordingState.isRecording 
            ? 'bg-gradient-to-br from-red-500/80 to-red-600/80 animate-pulse scale-110' 
            : 'bg-gradient-to-br from-blue-500/80 to-indigo-600/80 scale-100 hover:scale-105'
        }`}>
          <Mic2 className={`h-14 w-14 sm:h-18 sm:w-18 text-white transition-all duration-500 ${
            recordingState.isRecording ? 'animate-bounce' : 'hover:scale-110'
          }`} />
        </div>
        
        <div className="space-y-3 animate-fade-in-up">
          <h3 className="text-2xl sm:text-3xl font-bold text-white transition-all duration-300">
            {recordingState.isRecording ? 'Įrašoma...' : 'Pasiruošęs įrašyti'}
          </h3>
          <p className="text-base sm:text-lg text-white/70 max-w-lg transition-all duration-300 leading-relaxed">
            {recordingState.isRecording 
              ? 'Spauskite "Sustabdyti" norėdami baigti įrašymą'
              : 'Spauskite "Naujas pokalbis" norėdami pradėti naują audio įrašymą su automatine transkribavimo galimybe.'
            }
          </p>
        </div>
      </div>

      {/* Recording Controls */}
      <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-200">
        {!recordingState.isRecording ? (
          <button
            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}
            className="inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95"
          >
            <Plus className="h-6 w-6 transition-transform duration-200 group-hover:rotate-90" />
            <span>Naujas pokalbis</span>
          </button>
        ) : (
          <button
            onClick={onStopRecording}
            className="inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-red-500/80 via-red-600/70 to-red-700/80 hover:from-red-500/90 hover:via-red-600/80 hover:to-red-700/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-red-400/40 hover:border-red-300/50 transform hover:scale-105 active:scale-95"
          >
            <Square className="h-6 w-6" />
            <span>Sustabdyti įrašymą</span>
          </button>
        )}
      </div>

      {/* Recording Indicator */}
      {recordingState.isRecording && (
        <div className="flex items-center space-x-3 text-red-400 animate-fade-in-up animation-delay-400">
          <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
          <span className="text-base font-medium">Įrašoma...</span>
        </div>
      )}
    </div>
  );
}; 