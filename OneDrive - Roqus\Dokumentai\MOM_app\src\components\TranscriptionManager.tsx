import React, { useState } from 'react';
import { Play, Square, Clock, Users, FileText, Zap, AlertTriangle, CheckCircle2, Loader2, XCircle, Trash2, CheckCircle, Headphones } from 'lucide-react';
import { Meeting, TranscriptionStatus } from '../types/meeting';

interface TranscriptionManagerProps {
  meetings: Meeting[];
  onStartTranscription: (meetingId: string) => void;
  onCancelTranscription: (meetingId: string) => void;
  isTranscribing: boolean;
  currentTranscriptionId?: string | null;
  onDeleteMeeting: (meetingId: string) => void;
  onViewResults?: () => void;
}

export const TranscriptionManager: React.FC<TranscriptionManagerProps> = ({
  meetings,
  onStartTranscription,
  onCancelTranscription,
  isTranscribing,
  currentTranscriptionId,
  onDeleteMeeting,
  onViewResults,
}) => {
  const pendingMeetings = meetings.filter(m => 
    m.transcriptionStatus.state === 'not_started' || 
    m.transcriptionStatus.state === 'pending' ||
    m.transcriptionStatus.state === 'processing'
  );

  const completedMeetings = meetings.filter(m => 
    m.transcriptionStatus.state === 'completed'
  );

  const failedMeetings = meetings.filter(m => 
    m.transcriptionStatus.state === 'failed'
  );

  return (
    <div className="flex-1 flex flex-col space-y-8 animate-fade-in">
      {/* Pending Transcriptions */}
      {pendingMeetings.length > 0 && (
        <div className="space-y-5 animate-fade-in-up">
          <h3 className="text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3">
            <Clock className="h-6 w-6 text-yellow-400 animate-pulse" />
            <span>Laukia transkribavimo ({pendingMeetings.length})</span>
          </h3>
          <div className="space-y-4">
            {pendingMeetings.map((meeting, index) => (
              <div 
                key={meeting.id} 
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-white mb-2">{meeting.title}</h4>
                    <p className="text-base text-white/70 mb-2">
                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s
                    </p>
                    {meeting.transcriptionStatus.state === 'processing' && (
                      <div className="mt-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                          <span className="text-base text-blue-400">
                            Transkribuojama... {meeting.transcriptionStatus.progress || 0}%
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {meeting.transcriptionStatus.state === 'not_started' && (
                      <button
                        onClick={() => onStartTranscription(meeting.id)}
                        disabled={isTranscribing}
                        className="p-3 text-white/60 hover:text-green-400 hover:bg-green-500/10 rounded-lg transition-all duration-200 transform hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Pradėti transkribavimą"
                      >
                        <Play className="h-5 w-5" />
                      </button>
                    )}
                    {meeting.transcriptionStatus.state === 'processing' && currentTranscriptionId === meeting.id && (
                      <button
                        onClick={() => onCancelTranscription(meeting.id)}
                        className="p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110"
                        title="Atšaukti transkribavimą"
                      >
                        <Square className="h-5 w-5" />
                      </button>
                    )}
                    <button
                      onClick={() => onDeleteMeeting(meeting.id)}
                      className="p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110"
                      title="Ištrinti pokalbį"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Completed Transcriptions */}
      {completedMeetings.length > 0 && (
        <div className="space-y-5 animate-fade-in-up animation-delay-200">
          <div className="flex items-center justify-between">
            <h3 className="text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <span>Sėkmingai transkribuota ({completedMeetings.length})</span>
            </h3>
            {onViewResults && (
              <button
                onClick={onViewResults}
                className="inline-flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-green-500/80 via-green-600/70 to-emerald-600/80 hover:from-green-500/90 hover:via-green-600/80 hover:to-emerald-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-green-400/40 hover:border-green-300/50 transform hover:scale-105"
              >
                <Headphones className="h-4 w-4" />
                <span>Peržiūrėti rezultatus</span>
              </button>
            )}
          </div>
          <div className="space-y-4">
            {completedMeetings.map((meeting, index) => (
              <div 
                key={meeting.id} 
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-white mb-2">{meeting.title}</h4>
                    <p className="text-base text-white/70 mb-2">
                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s
                    </p>
                    {meeting.transcript && (
                      <p className="text-base text-green-400">
                        {meeting.transcript.length} segmentų • {meeting.participants?.length || 0} dalyvių
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => onDeleteMeeting(meeting.id)}
                    className="p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110"
                    title="Ištrinti pokalbį"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Failed Transcriptions */}
      {failedMeetings.length > 0 && (
        <div className="space-y-5 animate-fade-in-up animation-delay-400">
          <h3 className="text-xl sm:text-2xl font-semibold text-white flex items-center space-x-3">
            <XCircle className="h-6 w-6 text-red-400" />
            <span>Nepavyko transkribuoti ({failedMeetings.length})</span>
          </h3>
          <div className="space-y-4">
            {failedMeetings.map((meeting, index) => (
              <div 
                key={meeting.id} 
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.02] animate-fade-in-up"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-white mb-2">{meeting.title}</h4>
                    <p className="text-base text-white/70 mb-2">
                      {meeting.date.toLocaleString('lt-LT')} • {meeting.duration}s
                    </p>
                    {meeting.transcriptionStatus.error && (
                      <p className="text-base text-red-400">
                        Klaida: {meeting.transcriptionStatus.error}
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => onDeleteMeeting(meeting.id)}
                    className="p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110"
                    title="Ištrinti pokalbį"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {meetings.length === 0 && (
        <div className="flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up">
          <div className="w-20 h-20 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center animate-pulse">
            <Zap className="h-10 w-10 text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl sm:text-2xl font-semibold text-white">Nėra pokalbių</h3>
            <p className="text-base sm:text-lg text-white/70">
              Pradėkite naują pokalbį, kad pamatytumėte jį čia
            </p>
          </div>
        </div>
      )}
    </div>
  );
}; 