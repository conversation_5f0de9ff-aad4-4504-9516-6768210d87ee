import React from 'react';
import { Check<PERSON>ircle2, <PERSON>Circle, <PERSON><PERSON><PERSON><PERSON>gle, Zap, Check } from 'lucide-react';
import { isWhisperConfigured, getConfigStatus } from '../config/whisper';

export const WhisperStatusIndicator: React.FC = () => {
  const isConfigured = isWhisperConfigured();
  const status = getConfigStatus();
  const isConnected = isConfigured; // Assuming isConnected is derived from isConfigured

  return (
    <div className="inline-flex items-center space-x-1.5 px-2 py-1 bg-gradient-to-r from-green-500/20 via-emerald-500/15 to-green-500/20 backdrop-blur-md border border-green-400/30 rounded-md shadow-sm">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'} shadow-sm`}></div>
      <Zap className="h-3 w-3 text-green-300" />
      <span className="text-xs font-medium text-white/90">Whisper API</span>
      {isConnected && <Check className="h-3 w-3 text-green-300" />}
    </div>
  );
}; 