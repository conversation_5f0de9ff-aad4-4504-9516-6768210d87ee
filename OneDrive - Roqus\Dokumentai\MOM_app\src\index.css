@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font with optimal weights */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Custom Properties for Design System */
:root {
  /* Colors - Notion/Linear inspired */
  --color-bg-primary: #0a0a0a;
  --color-bg-secondary: #111111;
  --color-bg-tertiary: #1a1a1a;
  --color-bg-elevated: #222222;
  --color-bg-overlay: rgba(0, 0, 0, 0.8);

  --color-border-primary: rgba(255, 255, 255, 0.08);
  --color-border-secondary: rgba(255, 255, 255, 0.06);
  --color-border-focus: rgba(99, 102, 241, 0.4);

  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.7);
  --color-text-tertiary: rgba(255, 255, 255, 0.5);
  --color-text-placeholder: rgba(255, 255, 255, 0.3);

  --color-accent-primary: #6366f1;
  --color-accent-hover: #5855eb;
  --color-accent-light: rgba(99, 102, 241, 0.1);

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Typography Scale */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */

  /* Border Radius */
  --radius-sm: 0.375rem;  /* 6px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background: linear-gradient(135deg,
    var(--color-bg-primary) 0%,
    var(--color-bg-secondary) 25%,
    var(--color-bg-tertiary) 75%,
    var(--color-bg-elevated) 100%
  );
  background-attachment: fixed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Typography Classes */
.text-xs { font-size: var(--font-size-xs); line-height: var(--line-height-tight); }
.text-sm { font-size: var(--font-size-sm); line-height: var(--line-height-normal); }
.text-base { font-size: var(--font-size-base); line-height: var(--line-height-normal); }
.text-lg { font-size: var(--font-size-lg); line-height: var(--line-height-normal); }
.text-xl { font-size: var(--font-size-xl); line-height: var(--line-height-tight); }
.text-2xl { font-size: var(--font-size-2xl); line-height: var(--line-height-tight); }
.text-3xl { font-size: var(--font-size-3xl); line-height: var(--line-height-tight); }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }
.text-placeholder { color: var(--color-text-placeholder); }

/* Modern Background System */
.bg-app-gradient {
  background: linear-gradient(135deg,
    var(--color-bg-primary) 0%,
    var(--color-bg-secondary) 25%,
    var(--color-bg-tertiary) 75%,
    var(--color-bg-elevated) 100%
  );
  background-attachment: fixed;
  min-height: 100vh;
  position: relative;
}

.bg-primary { background-color: var(--color-bg-primary); }
.bg-secondary { background-color: var(--color-bg-secondary); }
.bg-tertiary { background-color: var(--color-bg-tertiary); }
.bg-elevated { background-color: var(--color-bg-elevated); }

/* Modern Card System */
.card {
  background: var(--color-bg-elevated);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.card:hover {
  border-color: var(--color-border-secondary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.card-compact {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

/* Modern Glassmorphism */
.glassmorphism {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--color-border-primary);
  box-shadow:
    var(--shadow-lg),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Basic utility classes */
.min-h-screen {
  min-height: 100vh;
}

.relative {
  position: relative;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.overflow-hidden {
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

.z-10 {
  z-index: 10;
  position: relative;
}

.z-50 {
  z-index: 50;
  position: relative;
}

/* Ensure content is above grid */
.relative.z-10 > * {
  position: relative;
  z-index: 2;
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1 1 0%;
}

/* Grid utilities */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

/* Spacing utilities */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* Padding utilities */
.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* Margin utilities */
.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

/* Width and height utilities */
.w-4 {
  width: 1rem;
}

.w-6 {
  width: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-full {
  width: 100%;
}

.h-4 {
  height: 1rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-full {
  height: 100%;
}

/* Text utilities */
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Text colors */
.text-white {
  color: rgb(255 255 255);
}

.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}

.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

/* Background colors */
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

/* Border utilities */
.border {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}

/* Border radius */
.rounded {
  border-radius: 0.25rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

/* Shadow utilities */
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Backdrop filter */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Transition utilities */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Transform utilities */
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: 0.95;
  --tw-scale-y: 0.95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Hover utilities */
.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:scale-95:active {
  --tw-scale-x: 0.95;
  --tw-scale-y: 0.95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Gradient backgrounds */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}

.bg-gradient-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}

.bg-gradient-radial {
  background-image: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Gradient color stops */
.from-purple-900\/06 {
  --tw-gradient-from: rgb(88 28 135 / 0.06);
  --tw-gradient-to: rgb(88 28 135 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-transparent {
  --tw-gradient-to: rgb(255 255 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), transparent, var(--tw-gradient-to);
}

.via-purple-800\/04 {
  --tw-gradient-to: rgb(107 33 168 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(107 33 168 / 0.04), var(--tw-gradient-to);
}

.to-slate-900\/08 {
  --tw-gradient-to: rgb(15 23 42 / 0.08);
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6;
  --tw-gradient-to: rgb(59 130 246 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-indigo-600 {
  --tw-gradient-to: #4f46e5;
}

.from-blue-500\/80 {
  --tw-gradient-from: rgb(59 130 246 / 0.8);
  --tw-gradient-to: rgb(59 130 246 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.via-blue-600\/70 {
  --tw-gradient-to: rgb(37 99 235 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(37 99 235 / 0.7), var(--tw-gradient-to);
}

.to-indigo-600\/80 {
  --tw-gradient-to: rgb(79 70 229 / 0.8);
}

.hover\:from-blue-500\/90:hover {
  --tw-gradient-from: rgb(59 130 246 / 0.9);
  --tw-gradient-to: rgb(59 130 246 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:via-blue-600\/80:hover {
  --tw-gradient-to: rgb(37 99 235 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(37 99 235 / 0.8), var(--tw-gradient-to);
}

.hover\:to-indigo-600\/90:hover {
  --tw-gradient-to: rgb(79 70 229 / 0.9);
}

/* Button base styles */
button {
  cursor: pointer;
  border: none;
  outline: none;
  font-family: inherit;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Common button classes */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
}

.btn:hover {
  transform: scale(1.05);
}

.btn:active {
  transform: scale(0.95);
}

/* Primary button */
.btn-primary {
  color: white;
  background: linear-gradient(to right, rgb(59 130 246 / 0.8), rgb(37 99 235 / 0.7), rgb(79 70 229 / 0.8));
  backdrop-filter: blur(12px);
  border-color: rgb(59 130 246 / 0.4);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.btn-primary:hover {
  background: linear-gradient(to right, rgb(59 130 246 / 0.9), rgb(37 99 235 / 0.8), rgb(79 70 229 / 0.9));
  border-color: rgb(59 130 246 / 0.5);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Grid page backgrounds with animated patterns */
.grid-page-recording {
  position: relative;
}

.grid-page-recording::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 120px 120px, rgba(59, 130, 246, 0.4) 2px, transparent 2px),
    radial-gradient(circle at 60px 60px, rgba(147, 51, 234, 0.3) 1px, transparent 1px);
  background-size: 120px 120px, 60px 60px;
  background-position: 0 0, 30px 30px;
  animation: grid-float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.grid-page-transcription {
  position: relative;
}

.grid-page-transcription::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 120px 120px, rgba(168, 85, 247, 0.4) 2px, transparent 2px),
    radial-gradient(circle at 60px 60px, rgba(236, 72, 153, 0.3) 1px, transparent 1px);
  background-size: 120px 120px, 60px 60px;
  background-position: 0 0, 30px 30px;
  animation: grid-pulse 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.grid-page-results {
  position: relative;
}

.grid-page-results::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 120px 120px, rgba(34, 197, 94, 0.4) 2px, transparent 2px),
    radial-gradient(circle at 60px 60px, rgba(16, 185, 129, 0.3) 1px, transparent 1px);
  background-size: 120px 120px, 60px 60px;
  background-position: 0 0, 30px 30px;
  animation: grid-wave 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.grid-page-home {
  position: relative;
}

.grid-page-home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 120px 120px, rgba(147, 51, 234, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 60px 60px, rgba(79, 70, 229, 0.2) 1px, transparent 1px);
  background-size: 120px 120px, 60px 60px;
  background-position: 0 0, 30px 30px;
  pointer-events: none;
  z-index: 1;
}

/* Grid animations */
@keyframes grid-float {
  0%, 100% {
    background-position: 0 0;
    opacity: 0.7;
  }
  50% {
    background-position: 20px 20px;
    opacity: 1;
  }
}

@keyframes grid-pulse {
  0%, 100% {
    background-position: 0 0;
    opacity: 0.6;
  }
  25% {
    background-position: 10px 0;
    opacity: 0.9;
  }
  75% {
    background-position: -10px 10px;
    opacity: 0.8;
  }
}

@keyframes grid-wave {
  0%, 100% {
    background-position: 0 0;
    opacity: 0.5;
  }
  33% {
    background-position: 15px 5px;
    opacity: 0.8;
  }
  66% {
    background-position: -5px 15px;
    opacity: 0.9;
  }
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fade-in-down 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounce-in 0.8s ease-out;
}

/* Animation delays */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* Smooth transitions */
.transition-ultra {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glassmorphism effects */
.glassmorphism {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Enhanced glassmorphism for headers */
.glassmorphism-header {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 2px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Elegant Navbar Styles */
.elegant-navbar-glass {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Elegant Logo Container */
.elegant-logo-container {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(139, 92, 246, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.elegant-logo-container::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.elegant-logo-container:hover::before {
  opacity: 1;
}

.elegant-logo-container:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.4),
    0 6px 16px rgba(139, 92, 246, 0.3);
}

/* Elegant Navigation Container */
.elegant-nav-container {
  position: relative;
}

.elegant-nav-pills {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Elegant Navigation Pills */
.elegant-nav-pill {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.elegant-nav-pill::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.elegant-nav-pill:hover::before {
  opacity: 1;
}

.elegant-nav-pill:hover {
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.elegant-nav-pill.active {
  color: white;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(139, 92, 246, 0.6) 100%);
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.elegant-nav-pill.active::before {
  opacity: 0;
}

/* Elegant Status Container */
.elegant-status-container {
  position: relative;
}

/* Elegant Whisper Status */
.elegant-whisper-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 14px;
  background: rgba(16, 185, 129, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.elegant-whisper-status::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.elegant-whisper-status:hover::before {
  opacity: 1;
}

/* Elegant Status Dot */
.elegant-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.elegant-status-dot.connected {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow:
    0 0 12px rgba(16, 185, 129, 0.6),
    0 0 24px rgba(16, 185, 129, 0.3);
  animation: pulse-glow 2s infinite;
}

.elegant-status-dot.disconnected {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow:
    0 0 12px rgba(239, 68, 68, 0.6),
    0 0 24px rgba(239, 68, 68, 0.3);
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* Elegant Demo Button */
.elegant-demo-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(245, 158, 11, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 12px;
  color: rgb(245, 158, 11);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.elegant-demo-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.elegant-demo-button:hover::before {
  opacity: 1;
}

.elegant-demo-button:hover {
  background: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.4);
  color: rgb(251, 191, 36);
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(245, 158, 11, 0.2),
    0 4px 12px rgba(245, 158, 11, 0.1);
}

/* Elegant Primary Button */
.elegant-primary-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(139, 92, 246, 0.2);
}

.elegant-primary-button::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.elegant-primary-button:hover::before {
  opacity: 1;
}

.elegant-primary-button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.4),
    0 6px 16px rgba(139, 92, 246, 0.3);
}

/* Responsive Design for Elegant Navbar */
@media (max-width: 768px) {
  .elegant-nav-pills {
    gap: 2px;
    padding: 4px;
  }

  .elegant-nav-pill {
    padding: 8px 12px;
    font-size: 13px;
  }

  .elegant-logo-container {
    width: 36px;
    height: 36px;
  }

  .elegant-primary-button {
    padding: 10px 16px;
    font-size: 13px;
  }

  .elegant-demo-button {
    padding: 8px 12px;
    font-size: 13px;
  }

  .elegant-whisper-status {
    padding: 6px 10px;
    font-size: 13px;
  }
}

@media (max-width: 640px) {
  .elegant-navbar-glass {
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
  }

  .elegant-nav-pills {
    padding: 3px;
  }

  .elegant-nav-pill {
    padding: 6px 8px;
    font-size: 12px;
  }

  .elegant-logo-container {
    width: 32px;
    height: 32px;
  }
}

/* Elegant Navbar Entrance Animation */
@keyframes navbar-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.elegant-navbar-glass {
  animation: navbar-slide-down 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Subtle hover effects for better UX */
.elegant-nav-container:hover .elegant-nav-pills {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Enhanced focus states for accessibility */
.elegant-nav-pill:focus-visible,
.elegant-demo-button:focus-visible,
.elegant-primary-button:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.6);
  outline-offset: 2px;
}

/* Logo animation */
.logo-icon {
  transition: all 0.2s ease-out;
}

.logo-icon:hover {
  transform: scale(1.05) rotate(5deg);
}

/* Gradient backgrounds */
.bg-unique-gradient-1 {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
}

.bg-unique-gradient-2 {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}

.bg-unique-gradient-3 {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
}

/* Shadow effects */
.shadow-elegant {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
}

.shadow-primary {
  box-shadow: 0 20px 40px rgba(147, 51, 234, 0.15), 0 8px 16px rgba(147, 51, 234, 0.1);
}

.shadow-soft {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Hover effects */
.hover-gradient-shift {
  transition: background 0.3s ease;
}

.hover-gradient-shift:hover {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(79, 70, 229, 0.15) 100%);
}

.float-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.float-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* Pulse effects */
.pulse-subtle {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Gradient borders */
.gradient-border-fade {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 1rem;
}

.gradient-border-fade::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.3) 0%, rgba(79, 70, 229, 0.3) 100%);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Button styles */
.btn-unique {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300;
}

.btn-unique-primary {
  @apply text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50;
}

.btn-unique-icon {
  @apply space-x-2;
}

.btn-unique-lg {
  @apply px-6 py-3 text-base font-semibold;
}

/* Responsive text */
@media (max-width: 640px) {
  .text-responsive {
    font-size: 0.875rem;
  }
}

@media (min-width: 641px) {
  .text-responsive {
    font-size: 1rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-transparent;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Additional gradient utilities */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Enhanced glassmorphism variants */
.glassmorphism-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.glassmorphism-subtle {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

/* Button hover effects */
.btn-hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Text gradients */
.text-gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Status indicators */
.status-recording {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
  animation: pulse 2s infinite;
}

.status-processing {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);
  animation: pulse 2s infinite;
}

.status-complete {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
}

/* Modern Responsive System */
/* Mobile First Approach */

/* Extra Small devices (phones, 475px and down) */
@media (max-width: 475px) {
  :root {
    --font-size-base: 0.875rem;
    --space-4: 0.75rem;
    --space-6: 1rem;
  }

  .xs\:hidden { display: none !important; }
  .xs\:block { display: block !important; }
  .xs\:flex { display: flex !important; }
  .xs\:grid { display: grid !important; }
  .xs\:text-xs { font-size: var(--font-size-xs) !important; }
  .xs\:text-sm { font-size: var(--font-size-sm) !important; }
  .xs\:p-2 { padding: var(--space-2) !important; }
  .xs\:p-3 { padding: var(--space-3) !important; }
  .xs\:gap-2 { gap: var(--space-2) !important; }
  .xs\:gap-3 { gap: var(--space-3) !important; }
}

/* Small devices (tablets, 640px and up) */
@media (min-width: 640px) {
  .sm\:hidden { display: none !important; }
  .sm\:block { display: block !important; }
  .sm\:flex { display: flex !important; }
  .sm\:grid { display: grid !important; }
  .sm\:text-sm { font-size: var(--font-size-sm) !important; }
  .sm\:text-base { font-size: var(--font-size-base) !important; }
  .sm\:text-lg { font-size: var(--font-size-lg) !important; }
  .sm\:p-4 { padding: var(--space-4) !important; }
  .sm\:p-6 { padding: var(--space-6) !important; }
  .sm\:gap-4 { gap: var(--space-4) !important; }
  .sm\:gap-6 { gap: var(--space-6) !important; }
}

/* Medium devices (laptops, 768px and up) */
@media (min-width: 768px) {
  .md\:hidden { display: none !important; }
  .md\:block { display: block !important; }
  .md\:flex { display: flex !important; }
  .md\:grid { display: grid !important; }
  .md\:text-base { font-size: var(--font-size-base) !important; }
  .md\:text-lg { font-size: var(--font-size-lg) !important; }
  .md\:text-xl { font-size: var(--font-size-xl) !important; }
  .md\:p-6 { padding: var(--space-6) !important; }
  .md\:p-8 { padding: var(--space-8) !important; }
  .md\:gap-6 { gap: var(--space-6) !important; }
  .md\:gap-8 { gap: var(--space-8) !important; }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
  .lg\:hidden { display: none !important; }
  .lg\:block { display: block !important; }
  .lg\:flex { display: flex !important; }
  .lg\:grid { display: grid !important; }
  .lg\:text-lg { font-size: var(--font-size-lg) !important; }
  .lg\:text-xl { font-size: var(--font-size-xl) !important; }
  .lg\:text-2xl { font-size: var(--font-size-2xl) !important; }
  .lg\:p-8 { padding: var(--space-8) !important; }
  .lg\:p-12 { padding: var(--space-12) !important; }
  .lg\:gap-8 { gap: var(--space-8) !important; }
  .lg\:gap-12 { gap: var(--space-12) !important; }
}

/* Extra Large devices (large desktops, 1280px and up) */
@media (min-width: 1280px) {
  .xl\:hidden { display: none !important; }
  .xl\:block { display: block !important; }
  .xl\:flex { display: flex !important; }
  .xl\:grid { display: grid !important; }
  .xl\:text-xl { font-size: var(--font-size-xl) !important; }
  .xl\:text-2xl { font-size: var(--font-size-2xl) !important; }
  .xl\:text-3xl { font-size: var(--font-size-3xl) !important; }
  .xl\:p-12 { padding: var(--space-12) !important; }
  .xl\:p-16 { padding: var(--space-16) !important; }
  .xl\:gap-12 { gap: var(--space-12) !important; }
  .xl\:gap-16 { gap: var(--space-16) !important; }
}

/* Modern Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  line-height: 1;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  outline: none;
}

.btn:focus-visible {
  outline: 2px solid var(--color-accent-primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Variants */
.btn-primary {
  background: var(--color-accent-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-accent-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-bg-tertiary);
  border-color: var(--color-border-secondary);
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--color-text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-bg-elevated);
  color: var(--color-text-primary);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  gap: var(--space-1);
}

.btn-lg {
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-base);
  gap: var(--space-3);
}

/* Icon Button */
.btn-icon {
  padding: var(--space-2);
  width: 2.5rem;
  height: 2.5rem;
}

.btn-icon.btn-sm {
  padding: var(--space-1);
  width: 2rem;
  height: 2rem;
}

/* Card styles */
.card {
  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg;
}

.card-header {
  @apply px-6 py-4 border-b border-white/10;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-white/10;
}

/* Input styles */
.input-primary {
  @apply w-full px-4 py-2 text-white bg-white/10 border border-white/20 rounded-lg backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 placeholder-white/50;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin;
}

/* Modern Tooltip System */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background: var(--color-bg-overlay);
  color: var(--color-text-primary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: 1000;
  backdrop-filter: blur(20px);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-lg);
}

.tooltip::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-2px);
  border: 4px solid transparent;
  border-top-color: var(--color-bg-overlay);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: 1000;
}

.tooltip:hover::before,
.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-4px);
}

/* Modern Input System */
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-md);
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: all var(--transition-fast);
  outline: none;
}

.input::placeholder {
  color: var(--color-text-placeholder);
}

.input:focus {
  border-color: var(--color-accent-primary);
  box-shadow: 0 0 0 3px var(--color-accent-light);
  background: var(--color-bg-tertiary);
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--color-bg-primary);
}

/* Input Sizes */
.input-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-xs);
}

.input-lg {
  padding: var(--space-4) var(--space-5);
  font-size: var(--font-size-base);
}

/* Modern Progress System */
.progress {
  width: 100%;
  height: 6px;
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-accent-primary), var(--color-accent-hover));
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Layout Utilities */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-1 { flex: 1 1 0%; }
.flex-none { flex: none; }

/* Spacing Utilities */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-5 { padding-left: var(--space-5); padding-right: var(--space-5); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }

.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }

.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

/* Z-index */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-50 { z-index: 50; }

/* Width & Height */
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-full { width: 100%; }

.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-full { height: 100%; }

/* Border Radius */
.rounded { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transitions */
.transition-all { transition: all var(--transition-fast); }
.transition-colors { transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast); }

/* Transforms */
.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-100 { opacity: 1; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

/* Cursor */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* User Select */
.select-none { user-select: none; }

/* Max Width */
.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }