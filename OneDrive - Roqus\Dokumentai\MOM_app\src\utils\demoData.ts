import { Meeting, TranscriptSegment } from '../types/meeting';

// Create a silent audio blob for demo purposes
const createDemoAudioBlob = (durationSeconds: number): Blob => {
  const sampleRate = 44100;
  const numChannels = 1;
  const numSamples = sampleRate * durationSeconds;
  
  const audioBuffer = new Float32Array(numSamples);
  
  // Create a simple sine wave for demo
  for (let i = 0; i < numSamples; i++) {
    const frequency = 440; // A4 note
    const amplitude = 0.1;
    audioBuffer[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * amplitude;
  }
  
  // Convert to WAV format (simplified)
  const wav = new ArrayBuffer(44 + audioBuffer.length * 2);
  const view = new DataView(wav);
  
  // WAV header
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };
  
  writeString(0, 'RIFF');
  view.setUint32(4, 36 + audioBuffer.length * 2, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * 2, true);
  view.setUint16(32, 2, true);
  view.setUint16(34, 16, true);
  writeString(36, 'data');
  view.setUint32(40, audioBuffer.length * 2, true);
  
  // Convert float samples to 16-bit PCM
  for (let i = 0; i < audioBuffer.length; i++) {
    const sample = Math.max(-1, Math.min(1, audioBuffer[i]));
    view.setInt16(44 + i * 2, sample * 0x7FFF, true);
  }
  
  return new Blob([wav], { type: 'audio/wav' });
};

const demoTranscript1: TranscriptSegment[] = [
  {
    id: 'demo-1',
    speaker: 'Jonas',
    text: 'Sveiki, šiandien aptarsime projekto pažangą ir artimiausius tikslus.',
    timestamp: 5,
    confidence: 0.95
  },
  {
    id: 'demo-2',
    speaker: 'Marija',
    text: 'Puiku! Esu paruošusi naują ataskaita apie klientų atsiliepimus.',
    timestamp: 15,
    confidence: 0.92
  },
  {
    id: 'demo-3',
    speaker: 'Petras',
    text: 'Taip pat turėčiau paminėti, kad mes perbaigėme dizaino fazę.',
    timestamp: 28,
    confidence: 0.88
  }
];

const demoTranscript2: TranscriptSegment[] = [
  {
    id: 'demo-4',
    speaker: 'Inga',
    text: 'Rytojaus susitikime aptarsime biudžeto klausimus.',
    timestamp: 3,
    confidence: 0.94
  },
  {
    id: 'demo-5',
    speaker: 'Tomas',
    text: 'Aš paruošiau išsamų biudžeto paskirstymo planą.',
    timestamp: 12,
    confidence: 0.91
  }
];

export const createDemoMeetings = (): Meeting[] => {
  const now = new Date();
  
  return [
    {
      id: 'demo-meeting-1',
      title: 'Savaitės planavimo susitikimas',
      date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      duration: 45,
      status: 'completed',
      audioBlob: createDemoAudioBlob(45),
      transcript: demoTranscript1,
      transcriptionStatus: {
        state: 'completed',
        progress: 100,
        startedAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000),
        completedAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000),
      }
    },
    {
      id: 'demo-meeting-2',
      title: 'Biudžeto aptarimas',
      date: new Date(now.getTime() - 24 * 60 * 60 * 1000), // 1 day ago
      duration: 25,
      status: 'completed',
      audioBlob: createDemoAudioBlob(25),
      transcript: demoTranscript2,
      transcriptionStatus: {
        state: 'completed',
        progress: 100,
        startedAt: new Date(now.getTime() - 24 * 60 * 60 * 1000 + 1 * 60 * 1000),
        completedAt: new Date(now.getTime() - 24 * 60 * 60 * 1000 + 3 * 60 * 1000),
      }
    },
    {
      id: 'demo-meeting-3',
      title: 'Kasdieninis standup',
      date: new Date(now.getTime() - 60 * 60 * 1000), // 1 hour ago
      duration: 15,
      status: 'completed',
      transcriptionStatus: {
        state: 'not_started',
      }
      // No audio blob for this one to show different states
    }
  ];
}; 